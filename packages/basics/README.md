# basics

Ce plugin propose une série d'instructions transformer plusieurs formats `text` (xml, json, cvs, etc.) en flux d'objets Javascript

## installation

```bash
npm install @ezs/basics
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [BIBParse](#bibparse)
*   [BUFObject](#bufobject)
*   [CSVObject](#csvobject)
*   [CSVParse](#csvparse)
*   [CSVString](#csvstring)
*   [FILELoad](#fileload)
*   [FILEMerge](#filemerge)
*   [FILESave](#filesave)
*   [INIString](#inistring)
*   [JSONParse](#jsonparse)
*   [JSONString](#jsonstring)
*   [OBJCount](#objcount)
*   [OBJFlatten](#objflatten)
*   [OBJNamespaces](#objnamespaces)
*   [OBJStandardize](#objstandardize)
*   [TARDump](#tardump)
*   [TARExtract](#tarextract)
*   [TXTConcat](#txtconcat)
*   [TXTObject](#txtobject)
*   [TXTParse](#txtparse)
*   [TXTZip](#txtzip)
*   [URLConnect](#urlconnect)
*   [URLFetch](#urlfetch)
*   [URLPagination](#urlpagination)
*   [URLParse](#urlparse)
*   [URLRequest](#urlrequest)
*   [URLStream](#urlstream)
*   [URLString](#urlstring)
*   [XMLConvert](#xmlconvert)
*   [XMLParse](#xmlparse)
*   [XMLString](#xmlstring)
*   [ZIPExtract](#zipextract)

### BIBParse

Take a `String` and split it at bibtext entry.

Input:

```json
["@article{my_article,\ntitle = {Hello world},\n", "journal = \"Some Journal\"\n"]
```

Output:

```json
["a", "b", "c", "d"]
```

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### BUFObject

Take `Mixed` and produce Buffer.
For example, it's useful to send string to browser.

#### Parameters

*   `none` **[undefined](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined)**&#x20;

Returns **[Buffer](https://nodejs.org/api/buffer.html)**&#x20;

### CSVObject

Take an `Array` of arrays and transform rows into objects.

Each row (Array) is tranformed into an object where keys are the values of
the first row.

See [CSVParse](#csvparse).

Input:

```json
[
  ["a", "b", "c"],
  [1, 2, 3],
  [4, 5, 6]
]
```

Output:

```json
[{
 "a": 1,
 "b": 2,
 "c": 3
}, {
 "a": 4,
 "b": 5,
 "c": 6
}]
```

> **Tip**: this is useful after a CSVParse, to convert raw rows into n array
> of Javascript objects

When several values of the first row are the same, produced keys are suffixed
with a number.

Input:

```json
[
  ["a", "a", "b", "b", "b"],
  [1, 2, 3, 4, 5]
]
```

Output:

```json
[{
   "a1": 1,
   "a2": 2,
   "b1": 3,
   "b2": 4,
   "b3": 5
}]
```

#### Parameters

*   `none` **[undefined](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined)**&#x20;

Returns **([Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object) | [Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>)**&#x20;

### CSVParse

Take `String` and parse it as CSV to generate arrays.

See:

*   [CSVObject](#csvobject)
*   <https://github.com/Inist-CNRS/node-csv-string>

Input:

```json
"a,b,c\nd,e,d\n"
```

Output:

```json
[
  ["a", "b", "c"],
  ["d", "e", "d"]
]
```

> **Tip**: see CSVObject, to convert arrays of values to array of objects.

#### Parameters

*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** to indicate the CSV separator (optional, default `auto`)
*   `quote` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** to indicate the CSV quote. (optional, default `auto`)

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>>**&#x20;

### CSVString

Take an array of objects and transform row into a string where each field is
separated with a character.

The resulting string is CSV-compliant.

See [CSVObject](#csvobject)

Input:

```json
[{
  "a": 1,
  "b": 2,
  "c": 3
}, {
  "a": 4,
  "b": 5,
  "c": 6
}]
```

Output:

```txt
a;b;c
1;2;3
4;5;6
```

#### Parameters

*   `format` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** if set to "strict" the fields will be
    wrapped with double quote (optional, default `standard`)
*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** to indicate the CSV separator (optional, default `";"`)
*   `header` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** first line contains key name (optional, default `true`)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### FILELoad

Take `Object` containing filename et throw content by chunk

```json
[ fi1e1.csv, file2.csv ]
```

Script:

```ini
[use]
plugin = analytics
plugin = basics

[FILELoad]
location = /tmp
[CSVParse]

```

Output:

```json
[
(...)
]
```

#### Parameters

*   `location` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Directory location (optional, default `TMPDIR`)
*   `compress` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Enable gzip compression (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### FILEMerge

Take `Object` or `Buffer` and throw only one document

```json
[ fi1e1.csv, file2.csv ]
```

Script:

```ini
[use]
plugin = basics

[FILELoad]
[FILEMerge]
[replace]
path = contentOfFile1AndFile2
value = self()

```

Output:

```json
[
(...)
]
```

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### FILESave

Take data, convert it to buffer and append it to file

##### Example

Input:

```json
[
  {"a": "a"},
  {"a": "b"},
  {"a": "c" }
]
```

Script:

```ini
[FILESave]
location = /tmp
identifier = toto
```

Output:

```json
[{ filename: "/tmp/toto", size: XXX, ... }]
```

#### Parameters

*   `location` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Directory location (optional, default `TMPDIR`)
*   `identifier` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** File name
*   `content` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Content to save instead of using input object
*   `jsonl` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Save as json line (optional, default `false`)
*   `compress` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Enable gzip compression (optional, default `false`)
*   `append` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Enable append mode (add content to an existing file) (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### INIString

Take `Object` and generate INI

Take an array of ezs's statements in JSON, and yield an ezs script in a
string.

Input:

```json
[
    { "param": 1, "section": { "arg1": "a", "arg2": "b" } },
    { "param": 1, "section": { "arg1": "a", "arg2": "b" } },
    { "section": { "arg1": "a", "arg2": true } },
    { "sec1": { "arg1": "a", "arg2": [3, 4, 5] }, "sec2": { "arg1": "a", "arg2": { "x": 1, "y": 2 } } },
    { "secvide1": {}, "secvide2": {} },
]
```

Output:

```ini
param = 1
[section]
arg1 = a
arg2 = b
param = 1

[section]
arg1 = a
arg2 = b

[section]
arg1 = a
arg2 = true

[sec1]
arg1 = a
arg2 = [3,4,5]

[sec2]
arg1 = a
arg2 = {"x":1,"y":2}

[secvide1]

[secvide2]
```

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### JSONParse

Parse a `String` to JSON and generate objects.

See <https://github.com/dominictarr/JSONStream>

##### Example 1: with separator

Input:

```json
["{ \"a\": 1, \"b\": 3 }", "{ \"a\": 2, \"b\": 4 }"]
```

Script:

```ini
[JSONParse]
separator = b
```

Output:

```json
[3, 4]
```

##### Example 2: without separator

Input:

```json
["{ \"a\": 1 }", "{ \"a\": 2 }"]
```

Output:

```json
[1, 2]
```

#### Parameters

*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** to split at every JSONPath found (optional, default `"*"`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### JSONString

Take an `Object` and generate a JSON string.

Input:

```json
[{ "a": 1 }, { "b": 2 }]
```

Output:

```json
"[{\"a\":1},{\"b\":2}]"
```

#### Parameters

*   `wrap` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** every document is wrapped into an array (optional, default `true`)
*   `indent` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** indent JSON (optional, default `false`)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### OBJCount

Count how many objects are received, and yield the total.

Input:

```json
["a", "b", "c", "d"]
```

Output:

```json
[4]
```

#### Parameters

*   `none` **[undefined](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined)**&#x20;

Returns **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)**&#x20;

### OBJFlatten

Flatten an `Object` with a path delimiting character.

See <https://www.npmjs.com/package/flat>

Input:

```json
[
  { "a": { "b": 1, "c": 2}},
  { "a": { "b": 3, "c": 4}}
]
```

Output:

```json
[
  { "a/b": 1, "a/c": 2 },
  { "a/b": 3, "a/c": 4 }
]
```

#### Parameters

*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** choose a character to flatten keys (optional, default `"/"`)
*   `reverse` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** unflatten instead of flatten keys (optional, default `false`)
*   `safe` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** preserve arrays and their contents, (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### OBJNamespaces

Take `Object` and throw the same object, all keys parsed to replace namespaces with their prefixes

> **Note:**  You can also parse values for specific keys (keys containing references to other keys)

```json
[
  {
   "http://purl.org/dc/terms/title": "Life is good",
   "http://purl.org/ontology/places#Countryl": "France",
 },
 {
   "http://purl.org/dc/terms/title": "The rising sun",
   "http://purl.org/ontology/places#Country": "Japan",
 },
 {
   "http://purl.org/dc/terms/title": "Dolce Vista",
   "http://purl.org/ontology/places#Country": "Italy",
 }
]
```

Script:

```ini
[use]
plugin = basics

[OBJNamespaces]
prefix = dc:
namespace = http://purl.org/dc/terms/

prefix = place:
namespace = http://purl.org/ontology/places#

```

Output:

```json
[
 {
   "dc:title": "Life is good",
   "place:Country": "France",
 },
 {
   "dc:title": "The rising sun",
   "place:Country": "Japan",
 },
 {
   "dc:title": "Dolce Vista",
   "place:Country": "Italy",
 }
]
```

#### Parameters

*   `prefix` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the alias for a namespace
*   `namespace` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the namespace to substitute by a prefix
*   `reference` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** a regex to find key that contains a namespace to substitute (optional, default `null`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### OBJStandardize

Standardize `Object`s so that each object have the same keys.

Input:

```json
[{ "a": 1, "b": 2},
 { "b": 2, "c": 3},
 { "a": 1, "c": 3}]
```

Output:

```json
[{ "a": 1, "b": 2, "c": ""},
 { "b": 2, "b": "", "c": 3},
 { "a": 1, "b": "", "c": 3}]
```

#### Parameters

*   `none` **[undefined](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined)**&#x20;

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### TARDump

Take all recevied objects and build a tar file

```json
{
}
```

#### Parameters

*   `manifest` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Location path to store files in the tarball
*   `location` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Location path to store files in the tarball (optional, default `data`)
*   `json` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Convert to JSON the content of each chunk (optional, default `true`)
*   `extension` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Choose extension fo each file (optional, default `json`)
*   `additionalFile` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Path to an additional file that will be add to tarball
*   `compress` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Enable gzip compression (optional, default `false`)

### TARExtract

Take the content of a tar file, extract some files.
The JSON object is sent to the output stream for each file.
It returns to the output stream

```json
{
   "id": "file name",
   "value": "file contents"
}
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Regex to select the files to extract (optional, default `"**\/*.json"`)
*   `json` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Parse as JSON the content of each file (optional, default `true`)
*   `text` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** The content of each file is converted to a string (otherwise it remains a buffer) (optional, default `true`)
*   `compress` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Enable gzip compression (optional, default `false`)

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<{id: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String), value: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)}>**&#x20;

### TXTConcat

Concatenate all `String` items into one string

Input:

```json
["a", "b"]
```

Output:

```json
["ab"]
```

#### Parameters

*   `none` **[undefined](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined)**&#x20;

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### TXTObject

Take an array of values and generate an array containing objects with the
given `key` and matching value from the input array.

Input:

```json
[1, "b"]
```

Output:

```json
[{ "value": 1 }, { "value": "b" }]
```

#### Parameters

*   `key` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** choose a the key name (optional, default `"value"`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### TXTParse

Take a `String` and split it at each separator found.

Input:

```json
["a\nb\n", "c\nd\n"]
```

Output:

```json
["a", "b", "c", "d"]
```

#### Parameters

*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** choose character which trigger the split (optional, default `"\n"`)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### TXTZip

Take a `String` and zip it.

Uses [gzip](https://fr.wikipedia.org/wiki/Gzip)
algorithm to compress strings.

#### Parameters

*   `unzip` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** to Unzip input (optional, default `false`)

Returns **[Buffer](https://nodejs.org/api/buffer.html)**&#x20;

### URLConnect

Take an `Object` and send it to an URL.

The output will be the returned content of URL.

Useful to send JSON data to an API and get results.

#### Parameters

*   `url` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** URL to fetch
*   `streaming` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Direct connection to the Object Stream server (disables the retries setting) (optional, default `false`)
*   `json` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Parse as JSON the content of URL (optional, default `false`)
*   `timeout` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Timeout in milliseconds (optional, default `1000`)
*   `noerror` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Ignore all errors (optional, default `false`)
*   `retries` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** The maximum amount of times to retry the connection (optional, default `5`)
*   `encoder` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** The statement to encode each chunk to a string (optional, default `dump`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### URLFetch

Add a new field to an `Object`, with the returned content of URL.

Or if no target is specified, the output will be the returned content of URL.

#### Parameters

*   `url` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** URL to fetch
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** if present select value to send (by POST)
*   `target` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** choose the key to set
*   `json` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** parse as JSON the content of URL (optional, default `false`)
*   `timeout` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** timeout in milliseconds (optional, default `1000`)
*   `mimetype` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** mimetype for value of path  (if presents) (optional, default `"application/json"`)
*   `noerror` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** ignore all errors, the target field will remain undefined (optional, default `false`)
*   `retries` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** The maximum amount of times to retry the connection (optional, default `5`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### URLPagination

Take `Object` and multiple it to make it one object per page

Input:

```json
[{"q": "a"}]
```

Script:

```ini
[URLRequest]
url = https://api.search.net

[URLPagination]
total = get('total')
```

Output:

```json
[
     {
         "q": "a",
         "total": 22
         "offset": 0,
         "pageNumber": 1,
         "totalPages", 3,
         "maxPages": 1000,
         "limit": 10
     },
     {
         "q": "a",
         "total": 22
         "offset": 10,
         "pageNumber": 2,
         "totalPages", 3,
         "maxPages": 1000,
         "limit": 10
     },
     {
         "q": "a",
         "total": 22
         "offset": 20,
         "pageNumber": 3,
         "totalPages", 3,
         "maxPages": 1000,
         "limit": 10
     }
 ]
```

#### Parameters

*   `total` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** total to use for the pagination (optional, default `0`)
*   `limit` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** limit to use to pagination (optional, default `10`)
*   `maxPages` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** maxPages to use to pagination (optional, default `1000`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### URLParse

Take an URL `String`, parse it and return `Object`.

Fields of the returned object:

*   href
*   origin
*   protocol
*   username
*   password
*   host
*   hostname
*   port
*   pathname
*   search
*   hash

URLString statement convert such an object to a string.

See:

*   [URLString](#urlstring)
*   <https://nodejs.org/api/url.html>

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### URLRequest

Take `Object` as parameters of URL, throw each chunk from the result

Input:

```json
[{"q": "a"}]
```

Script:

```ini
[URLRequest]
url = https://api.search.net
```

Output:

```json
[
     {
         "result": "a"
     }
 ]
```

#### Parameters

*   `url` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** URL to fetch
*   `json` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** parse result as json (optional, default `true`)
*   `target` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** choose the key to set
*   `timeout` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Timeout in milliseconds (optional, default `1000`)
*   `noerror` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Ignore all errors, the target field will remain undefined (optional, default `false`)
*   `retries` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** The maximum amount of times to retry the connection (optional, default `5`)
*   `insert` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** a header response value in the result

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### URLStream

Take `String` as URL, throw each chunk from the result or
Take `Object` as parameters of URL, throw each chunk from the result

Next examples use an API `https://httpbin.org/get?a=n` returning

```json
{ args: { "a": "n" }}
```

##### Example with objects

Input:

```json
[{"a": "a"}, {"a": "b"}, {"a": "c" }]
```

Script:

```ini
[URLStream]
url = https://httpbin.org/get
path = .args
```

Output:

```json
[{"a": "a"}, {"a": "b"}, {"a": "c" }]
```

##### Example with URLs

Input:

```json
[
  "https://httpbin.org/get?a=a",
  "https://httpbin.org/get?a=b",
  "https://httpbin.org/get?a=c"
]
```

Script:

```ini
[URLStream]
path = .args
```

Output:

```json
[{"a": "a"}, {"a": "b"}, {"a": "c" }]
```

#### Parameters

*   `url` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** URL to fetch (by default input string is taken)
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** choose the path to split JSON result (optional, default `"*"`)
*   `timeout` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Timeout in milliseconds (optional, default `1000`)
*   `noerror` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Ignore all errors, the target field will remain undefined (optional, default `false`)
*   `retries` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** The maximum amount of times to retry the connection (optional, default `5`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### URLString

Take an `Object` representing an URL and stringify it.

See [URLParse](#urlparse)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### XMLConvert

Convert each chunk as XML String to JSON Object

##### Example 1: XML to JSON (default parameters)

Input:

```json
[
  "<xml>A</xml>",
  "<xml>B</xml>"
]
```

Output:

```json
[
  { "xml": { "$t": "A" } },
  { "xml": { "$t": "B" } }
]
```

##### Example 2: JSON to XML (invert parameter true)

Input:

```json
[
  { "x": { "a": 1 } },
  { "x": { "a": 2 } }
]
```

Output:

```json
[
  "<x a=\"1\"/>",
  "<x a=\"2\"/>",
]
```

##### Example 3: JSON to XML (prologue and invert true)

Input:

```json
[
  { "x": { "a": 1 } },
  { "x": { "a": 2 } }
]
```

Output:

```json
[
  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<x a=\"1\"/>",
  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<x a=\"2\"/>",
]
```

See <https://www.npmjs.com/package/xml-mapping>

#### Parameters

*   `invert` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** change conversion (JSON to XML) (optional, default `false`)
*   `prologue` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** add XML prologue (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### XMLParse

Take `String` as XML input, parse it and split it in multi document at each path found

Input:

```json
<* ["<a><b>x</b><b>y</b></a>"]
```

Script:

```ini
[XMLParse]
separator: /a/b
```

Output:

```json
["x", "y"]
```

See <https://www.npmjs.com/package/xml-splitter>

#### Parameters

*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** choose a character for flatten keys (optional, default `"/"`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### XMLString

Transform an `Object` into an XML string.

Input:

```json
[{ "$t": "a" }]
```

Output:

```json
[
  "<items><item>a</item></items>"
]
```

See [XMLParse](#xmlparse)

#### Parameters

*   `rootElement` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Root element name for the tag which starts and close the feed (optional, default `"items"`)
*   `contentElement` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Content element name for the tag which starts and closes each item (optional, default `"item"`)
*   `rootNamespace` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Namespace for the root tag (xmlns=)
*   `prologue` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Add XML prologue `<?xml` (optional, default `false`)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### ZIPExtract

Take the content of a zip file, extract some files.
The JSON object is sent to the output stream for each file.
It returns to the output stream

```json
{
   "id": "file name",
   "value": "file contents"
}
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Regex to select the files to extract (optional, default `"**\/*.json"`)

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<{id: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String), value: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)}>**&#x20;
