# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.9.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.9.1...@ezs/basics@2.9.2) (2025-08-29)

**Note:** Version bump only for package @ezs/basics





## [2.9.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.9.0...@ezs/basics@2.9.1) (2025-01-06)

**Note:** Version bump only for package @ezs/basics





# [2.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.8.2...@ezs/basics@2.9.0) (2024-12-20)


### Features

* 🎸 add append paramater to [filesave] ([b010a75](https://github.com/Inist-CNRS/ezs/commit/b010a754cb6c66ce937c906bc7df56e7c92e1180))





## [2.8.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.8.1...@ezs/basics@2.8.2) (2024-12-20)


### Bug Fixes

* 🐛 avoid crash with bad data ([0a7e997](https://github.com/Inist-CNRS/ezs/commit/0a7e99750c7ef9af175cb2fd7e89789067522826))





## [2.8.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.8.0...@ezs/basics@2.8.1) (2024-11-22)

**Note:** Version bump only for package @ezs/basics





# [2.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.7.2...@ezs/basics@2.8.0) (2024-11-05)


### Bug Fixes

* 🐛 files no longer exists ([5207c41](https://github.com/Inist-CNRS/ezs/commit/5207c41ca65f9ba6d7f685e30f01379446d20dbc))


### Features

* 🎸 TXTInflection & TXTSentences move to @ezs/strings ([3dd3df0](https://github.com/Inist-CNRS/ezs/commit/3dd3df0fec79e57ba24e0b560e83479b9ea73ac7))





## [2.7.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.7.1...@ezs/basics@2.7.2) (2024-09-18)

**Note:** Version bump only for package @ezs/basics





## [2.7.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.7.0...@ezs/basics@2.7.1) (2024-07-09)


### Bug Fixes

* 🐛 explicit error message for empty response ([0cc34fd](https://github.com/Inist-CNRS/ezs/commit/0cc34fdbdf76c7d40c80c8a20029ede05a091094))





# [2.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.6.1...@ezs/basics@2.7.0) (2024-07-04)


### Features

* 🎸 add FILEMerge ([cb879fa](https://github.com/Inist-CNRS/ezs/commit/cb879fae73b48a2b5850c6a0597a4ca97d722493))
* 🎸 keep buffer for binary files ([e4a30fe](https://github.com/Inist-CNRS/ezs/commit/e4a30fe729257af877ca2d9f89a6a948d62965d6))





## [2.6.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.6.0...@ezs/basics@2.6.1) (2024-02-07)

**Note:** Version bump only for package @ezs/basics





# [2.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.8...@ezs/basics@2.6.0) (2024-01-26)


### Bug Fixes

* 🐛 missing files ([be5b984](https://github.com/Inist-CNRS/ezs/commit/be5b984833f58dff1dcb28b9d998976094459e79))


### Features

* 🎸 add [BIBParse] ([20489b8](https://github.com/Inist-CNRS/ezs/commit/20489b8c816edc7e504c81e836ae558b7a524bcf))





## [2.5.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.7...@ezs/basics@2.5.8) (2023-12-19)


### Bug Fixes

* 🐛 avoid to produce null with undefined value ([14798ad](https://github.com/Inist-CNRS/ezs/commit/14798ad15faaaf51d8f2cacf202da62904cf049f))





## [2.5.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.6...@ezs/basics@2.5.7) (2023-12-08)


### Bug Fixes

* 🐛 1 retry not equal to 0 retry ([cb3f99b](https://github.com/Inist-CNRS/ezs/commit/cb3f99bfd50d95cec6aaadf52cab9bfced48c88f))





## [2.5.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.5...@ezs/basics@2.5.6) (2023-12-08)

**Note:** Version bump only for package @ezs/basics





## [2.5.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.4...@ezs/basics@2.5.5) (2023-09-29)


### Bug Fixes

* 🐛 missing string decoder ([b50f1a2](https://github.com/Inist-CNRS/ezs/commit/b50f1a2963c3266a0294dad862cbff34e501f3c2))





## [2.5.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.3...@ezs/basics@2.5.4) (2023-09-08)

**Note:** Version bump only for package @ezs/basics





## [2.5.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.2...@ezs/basics@2.5.3) (2023-08-25)


### Bug Fixes

* 🐛 tar extract freeze ([086b49f](https://github.com/Inist-CNRS/ezs/commit/086b49f7b3c24f551584514869bbe9ec135f7d0a))





## [2.5.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.1...@ezs/basics@2.5.2) (2023-08-25)

**Note:** Version bump only for package @ezs/basics





## [2.5.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.5.0...@ezs/basics@2.5.1) (2023-08-24)


### Bug Fixes

* 🐛 FILESave and direct buffer input ([d4c14ee](https://github.com/Inist-CNRS/ezs/commit/d4c14ee229f6699f43592b87b01b2fc9baf83c70))
* 🐛 remove buggy option ([3782390](https://github.com/Inist-CNRS/ezs/commit/378239008ed74d2aace22ecb3f3010e66f5af76a))





# [2.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.4.1...@ezs/basics@2.5.0) (2023-08-18)


### Features

* 🎸 support text files ([2ba8571](https://github.com/Inist-CNRS/ezs/commit/2ba8571dbd651849ab12c4c08627ff29019e3c7f))





## [2.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.4.0...@ezs/basics@2.4.1) (2023-07-20)


### Bug Fixes

* 🐛 manifest.json ([559cda8](https://github.com/Inist-CNRS/ezs/commit/559cda8372c65c3c1cfb5a0dcfee404ece4a79d6))





# [2.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.3.0...@ezs/basics@2.4.0) (2023-07-17)


### Features

* 🎸 add [TARDump] ([d3ff2a9](https://github.com/Inist-CNRS/ezs/commit/d3ff2a99650fc7c9eacd2c1ad015d9907addeebb))





# [2.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.2.0...@ezs/basics@2.3.0) (2023-06-23)


### Features

* **strings:** Add inflection and sentences statements ([67ba36c](https://github.com/Inist-CNRS/ezs/commit/67ba36c8ba68c1798f5430a087172fed45d2b3c0))





# [2.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.1.3...@ezs/basics@2.2.0) (2023-06-22)


### Features

* 🎸 add tar-extract ([a471aca](https://github.com/Inist-CNRS/ezs/commit/a471aca64b7ab08d92237917264a23080a2c3072))





## [2.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.1.2...@ezs/basics@2.1.3) (2023-05-30)

**Note:** Version bump only for package @ezs/basics





## [2.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.1.1...@ezs/basics@2.1.2) (2023-05-30)


### Bug Fixes

* 🐛 retry without json parse ([90e17b5](https://github.com/Inist-CNRS/ezs/commit/90e17b5ea2cbf60f18a9ecbf29b338b1395e318e))





## [2.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.1.0...@ezs/basics@2.1.1) (2023-05-12)

**Note:** Version bump only for package @ezs/basics





# [2.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@2.0.0...@ezs/basics@2.1.0) (2023-04-12)


### Bug Fixes

* 🐛 improve coverage ([1173137](https://github.com/Inist-CNRS/ezs/commit/1173137e5a2cd6f1f6bfda8ad2a89720ae991e91))
* 🐛 missing files ([cd091ac](https://github.com/Inist-CNRS/ezs/commit/cd091acd91a6b0042e6c0e1b9cfef87e64126007))


### Features

* 🎸 add [TXTInflection] ([880b2bc](https://github.com/Inist-CNRS/ezs/commit/880b2bc4795f8bfb51fe11803cebfe38d2097487))





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.24.0...@ezs/basics@2.0.0) (2023-03-29)


### Features

* **basics:** Make TXTSentences pass some tests ([95bd41f](https://github.com/Inist-CNRS/ezs/commit/95bd41f836e5ee2f71c8413142df9ac4dd9ec84b))
* **basics:** Make TXTSentences work with arrays too ([24c3f76](https://github.com/Inist-CNRS/ezs/commit/24c3f76be73a518791e59b7e78c4d1151af8edd6))


### BREAKING CHANGES

* **basics:** TXTSentences xxpected structure is now an object, with a value key.





# [1.24.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.23.5...@ezs/basics@1.24.0) (2023-03-28)


### Features

* **basics:** Add TXTSentences ([c66abb5](https://github.com/Inist-CNRS/ezs/commit/c66abb5f242afcff57f98a7fa8eff918a7d60098))





## [1.23.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.23.4...@ezs/basics@1.23.5) (2023-03-24)


### Bug Fixes

* 🐛 multi bytes in txtconcat ([7c705fa](https://github.com/Inist-CNRS/ezs/commit/7c705facc1378fae709bbae6a2b416b059e81731))





## [1.23.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.23.3...@ezs/basics@1.23.4) (2023-03-24)


### Bug Fixes

* 🐛 CSV.stream not support multi-byte utf8 characters ([1e15c9c](https://github.com/Inist-CNRS/ezs/commit/1e15c9c9061f8927d67bec36577e7c0d5bb15d8e))





## [1.23.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.23.2...@ezs/basics@1.23.3) (2023-02-27)


### Bug Fixes

* 🐛 fetch timeout vs feed timeout ([163e0bf](https://github.com/Inist-CNRS/ezs/commit/163e0bf81df5f3d8ef7d2fedc6b8bf95ec2c8534))





## [1.23.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.23.1...@ezs/basics@1.23.2) (2023-02-17)


### Bug Fixes

* 🐛 timeout never closed ([0c8597a](https://github.com/Inist-CNRS/ezs/commit/0c8597a11db0c3689a34888a1fd3e7807cc975db))





## [1.23.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.23.0...@ezs/basics@1.23.1) (2023-01-28)


### Bug Fixes

* 🐛 improve security in file access ([6942a8e](https://github.com/Inist-CNRS/ezs/commit/6942a8e039a05d820d8643fa0908287a0a6aefda))
* 🐛 objstandarize lost boolean values ([dca4714](https://github.com/Inist-CNRS/ezs/commit/dca4714c984214f422f9ed8951575de66af0f0ce))


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





# [1.23.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.7...@ezs/basics@1.23.0) (2023-01-20)


### Features

* 🎸 add new parameter to FILESave ([8fae7b8](https://github.com/Inist-CNRS/ezs/commit/8fae7b8009824d8e73c98e8277e15ffacd87d572))





## [1.22.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.6...@ezs/basics@1.22.7) (2023-01-13)


### Bug Fixes

* 🐛 backpresure control ([70c4a07](https://github.com/Inist-CNRS/ezs/commit/70c4a07010c0b1927bb57c95b3c5113e8aaa7552))





## [1.22.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.5...@ezs/basics@1.22.6) (2023-01-05)

**Note:** Version bump only for package @ezs/basics





## [1.22.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.4...@ezs/basics@1.22.5) (2022-12-21)


### Bug Fixes

* 🐛 timeout and retry error ([efa2cfa](https://github.com/Inist-CNRS/ezs/commit/efa2cfaab4b827700d8e89d1bc59cfb266237ec2))





## [1.22.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.3...@ezs/basics@1.22.4) (2022-12-02)


### Bug Fixes

* on retry, input stream was lost ([1b710f1](https://github.com/Inist-CNRS/ezs/commit/1b710f19d1dd163bcaf3580f0d23def6f948423a))





## [1.22.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.2...@ezs/basics@1.22.3) (2022-09-19)


### Bug Fixes

* 🐛 locations like [files] ([42fb184](https://github.com/Inist-CNRS/ezs/commit/42fb184bba3534bf2123a12d276d89f6809d09a7))





## [1.22.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.1...@ezs/basics@1.22.2) (2022-09-14)


### Bug Fixes

* 🐛 feed.flow return a Promise ([e73f140](https://github.com/Inist-CNRS/ezs/commit/e73f14042eb60b464c9e021345e479d24ba0ec81))





## [1.22.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.22.0...@ezs/basics@1.22.1) (2022-09-07)


### Bug Fixes

* 🐛 stop loop (and refactoring) ([2c162aa](https://github.com/Inist-CNRS/ezs/commit/2c162aa64a4603f79c5bdddbca8b58272278f745))





# [1.22.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.21.0...@ezs/basics@1.22.0) (2022-09-05)


### Bug Fixes

* use location to check filename ([cb9c6e2](https://github.com/Inist-CNRS/ezs/commit/cb9c6e2f42d12c287d806b4ccebf8a4f8607e755))


### Features

* 🎸 add file load ([a06c221](https://github.com/Inist-CNRS/ezs/commit/a06c221c3463b4299383db5c613f5cabc687961c))





# [1.21.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.20.0...@ezs/basics@1.21.0) (2022-07-11)


### Features

* 🎸 add target option to URLRequest ([3861c5e](https://github.com/Inist-CNRS/ezs/commit/3861c5e10b4dcfd060b49fa9b23f2bcf98d8c942))





# [1.20.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.19.0...@ezs/basics@1.20.0) (2022-06-24)


### Features

* 🎸 add [URLPager] ([59dcbdc](https://github.com/Inist-CNRS/ezs/commit/59dcbdca0c33eaf2ebf5a195de153cec802d1bc1))





# [1.19.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.18.0...@ezs/basics@1.19.0) (2022-06-22)


### Features

* 🎸 file save gz ([33df6cd](https://github.com/Inist-CNRS/ezs/commit/33df6cdc40af50c10511cca1449c5edb3a6878f2))





# [1.18.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.17.1...@ezs/basics@1.18.0) (2022-06-21)


### Features

* 🎸 add [FILESave] ([2e7dfd7](https://github.com/Inist-CNRS/ezs/commit/2e7dfd76f769418a56f7b9b6cb23d156f37b6789))





## [1.17.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.17.0...@ezs/basics@1.17.1) (2022-04-01)


### Bug Fixes

* erratic error with store ([a26febc](https://github.com/Inist-CNRS/ezs/commit/a26febc4fe7bc0a66a7d32781dc6ef175f707f0a))





# [1.17.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.16.0...@ezs/basics@1.17.0) (2022-03-25)


### Features

* 🎸 add retry feature ([166c913](https://github.com/Inist-CNRS/ezs/commit/166c913302b4d305ea34d3b84f9b8be6b5188cbb))





# [1.16.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.15.5...@ezs/basics@1.16.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.15.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.15.4...@ezs/basics@1.15.5) (2022-02-04)


### Bug Fixes

* 🐛 improve warning debug message on error ([5ff32e8](https://github.com/Inist-CNRS/ezs/commit/5ff32e8e2e6c48913d8d1abd35b9cee68ff19be4))





## [1.15.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.15.3...@ezs/basics@1.15.4) (2022-01-31)


### Bug Fixes

* npm dependencies ([36c7ebe](https://github.com/Inist-CNRS/ezs/commit/36c7ebea03e82e9d177f7480f587511360e3dace))





## [1.15.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.15.2...@ezs/basics@1.15.3) (2022-01-31)

**Note:** Version bump only for package @ezs/basics





## [1.15.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.15.1...@ezs/basics@1.15.2) (2022-01-27)

**Note:** Version bump only for package @ezs/basics





## [1.15.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.15.0...@ezs/basics@1.15.1) (2021-10-05)


### Bug Fixes

* 🐛 fix wrong options + miss dep.x ([5b38d05](https://github.com/Inist-CNRS/ezs/commit/5b38d05199a9a49c73d264f4ddb9a45dd0e64c7e))





# [1.15.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.14.0...@ezs/basics@1.15.0) (2021-10-05)


### Features

* 🎸 introduce new parser ([66a408b](https://github.com/Inist-CNRS/ezs/commit/66a408b0be2f6f2374d7193df2576e2fa383d369))





# [1.14.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.6...@ezs/basics@1.14.0) (2021-08-27)


### Features

* 🎸 support http headers ([0d11b7c](https://github.com/Inist-CNRS/ezs/commit/0d11b7c46a44040c48aa0d0fcb7954611dd36658))





## [1.13.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.5...@ezs/basics@1.13.6) (2021-07-30)

**Note:** Version bump only for package @ezs/basics





## [1.13.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.4...@ezs/basics@1.13.5) (2021-07-16)

**Note:** Version bump only for package @ezs/basics





## [1.13.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.3...@ezs/basics@1.13.4) (2021-07-15)

**Note:** Version bump only for package @ezs/basics





## [1.13.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.2...@ezs/basics@1.13.3) (2021-06-28)

**Note:** Version bump only for package @ezs/basics





## [1.13.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.1...@ezs/basics@1.13.2) (2021-06-04)


### Bug Fixes

* 🐛 add prologue option ([d1f461a](https://github.com/Inist-CNRS/ezs/commit/d1f461a637bab5b8c701a724a04776380bc29153))





## [1.13.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.13.0...@ezs/basics@1.13.1) (2021-06-04)

**Note:** Version bump only for package @ezs/basics





# [1.13.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.12.0...@ezs/basics@1.13.0) (2021-05-27)


### Features

* 🎸 add [XMLConvert] ([0040fce](https://github.com/Inist-CNRS/ezs/commit/0040fce60affd263f537853e5fd4957df43d6c7a))





# [1.12.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.11.2...@ezs/basics@1.12.0) (2021-04-26)


### Features

* 🎸 improve [URLFetch] ([6f487a6](https://github.com/Inist-CNRS/ezs/commit/6f487a641771b4a23660a7a870c240e531aa2ff9))





## [1.11.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.11.1...@ezs/basics@1.11.2) (2021-04-20)

**Note:** Version bump only for package @ezs/basics





## [1.11.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.11.0...@ezs/basics@1.11.1) (2021-04-09)

**Note:** Version bump only for package @ezs/basics





# [1.11.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.10.0...@ezs/basics@1.11.0) (2021-04-08)


### Features

* 🎸 add noerror option in URLConnect ([a70909b](https://github.com/Inist-CNRS/ezs/commit/a70909ba70ddcbe7c3da4dea4dea579a69a3bf46))





# [1.10.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.9.1...@ezs/basics@1.10.0) (2021-04-07)


### Bug Fixes

* 🐛 security patch ([37b826b](https://github.com/Inist-CNRS/ezs/commit/37b826bf8481b5fa92e00c43420037df6edebba6))


### Features

* add option noerror ([59b5916](https://github.com/Inist-CNRS/ezs/commit/59b5916bce96d861adb9500c513305892fbcecb3))





## [1.9.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.9.0...@ezs/basics@1.9.1) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))





# [1.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.8.0...@ezs/basics@1.9.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





# [1.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.7.4...@ezs/basics@1.8.0) (2021-03-03)


### Features

* 🎸 Add timeout option for statements using fetch ([3f0b28a](https://github.com/Inist-CNRS/ezs/commit/3f0b28a85943b081084fd19e7c78e6d554f51c0a))





## [1.7.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.7.3...@ezs/basics@1.7.4) (2021-01-29)

**Note:** Version bump only for package @ezs/basics





## [1.7.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.7.2...@ezs/basics@1.7.3) (2020-12-22)

**Note:** Version bump only for package @ezs/basics





## [1.7.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.7.1...@ezs/basics@1.7.2) (2020-12-21)


### Bug Fixes

* 🐛 upgrade package ([cf55a34](https://github.com/Inist-CNRS/ezs/commit/cf55a34b29d0c1e7dd02a224a49339967c452496))





## [1.7.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.7.0...@ezs/basics@1.7.1) (2020-12-17)


### Bug Fixes

* better system to close handles ([d826741](https://github.com/Inist-CNRS/ezs/commit/d826741277af2681b1e2bbb3dd6355913750ea8f))





# [1.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.6.5...@ezs/basics@1.7.0) (2020-12-07)


### Features

* 🎸 add unzip option to [TXTZip] ([8bf1a7e](https://github.com/Inist-CNRS/ezs/commit/8bf1a7e82b01b7165a9da2189a82bbaa82a92277))





## [1.6.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.6.4...@ezs/basics@1.6.5) (2020-11-13)


### Bug Fixes

* 🐛 cleartimeout ([9c1aaee](https://github.com/Inist-CNRS/ezs/commit/9c1aaee1a33543744cb0a7fb056038f5c1764f7f))





## [1.6.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.6.3...@ezs/basics@1.6.4) (2020-11-13)


### Bug Fixes

* 🐛 [URLStream] works without url parameter ([0b92988](https://github.com/Inist-CNRS/ezs/commit/0b92988c1f18e1875d9ac07155e4f5b5ea460de6))





## [1.6.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.6.2...@ezs/basics@1.6.3) (2020-10-26)

**Note:** Version bump only for package @ezs/basics





## [1.6.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.6.1...@ezs/basics@1.6.2) (2020-10-26)

**Note:** Version bump only for package @ezs/basics





## [1.6.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.6.0...@ezs/basics@1.6.1) (2020-10-23)


### Bug Fixes

* 🐛 handle leaked ([c343c5c](https://github.com/Inist-CNRS/ezs/commit/c343c5c7949b3d063f65803130066934f96ce6c6))





# [1.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.5.0...@ezs/basics@1.6.0) (2020-10-20)


### Bug Fixes

* 🐛 wrong import ([131e0f6](https://github.com/Inist-CNRS/ezs/commit/131e0f6090dfe8b6a53746533ea1c5f73f346c6e))


### Features

* 🎸 add reverse option to [OBJFlatten] ([e275607](https://github.com/Inist-CNRS/ezs/commit/e275607611957f6a3dccaca09b2ba45f287c3c9a))





# [1.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.12...@ezs/basics@1.5.0) (2020-10-19)


### Bug Fixes

* 🐛 missing files ([d6e8ae2](https://github.com/Inist-CNRS/ezs/commit/d6e8ae27592e56077729daa9872159253208665f))


### Features

* 🎸 add [OBJNamespaces] ([93469a4](https://github.com/Inist-CNRS/ezs/commit/93469a40e59697d843975244fd98fe48c2e2ea88))





## [1.4.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.11...@ezs/basics@1.4.12) (2020-10-05)

**Note:** Version bump only for package @ezs/basics





## [1.4.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.10...@ezs/basics@1.4.11) (2020-09-28)


### Bug Fixes

* 🐛 create anonymous object to avoid to inject internal OBJ ([ac313d8](https://github.com/Inist-CNRS/ezs/commit/ac313d85464eb372f97326508838cd932e9fa69c))





## [1.4.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.9...@ezs/basics@1.4.10) (2020-09-28)


### Bug Fixes

* 🐛 security patch ([06468d5](https://github.com/Inist-CNRS/ezs/commit/06468d56d76c640fb03d7fa73f72d9cc38d44675))
* avoid to inject internal nodejs class in the pipeline ([de00884](https://github.com/Inist-CNRS/ezs/commit/de008840b01379010991fffa37193c54fecf3cef))
* avoid to throw error with the last chunk (null value) ([243179e](https://github.com/Inist-CNRS/ezs/commit/243179e3018cb2115453636c17971486a55bef83))





## [1.4.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.8...@ezs/basics@1.4.9) (2020-09-17)

**Note:** Version bump only for package @ezs/basics





## [1.4.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.7...@ezs/basics@1.4.8) (2020-09-16)

**Note:** Version bump only for package @ezs/basics





## [1.4.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.6...@ezs/basics@1.4.7) (2020-09-14)

**Note:** Version bump only for package @ezs/basics





## [1.4.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.5...@ezs/basics@1.4.6) (2020-09-08)


### Bug Fixes

* close input to avoid dead lock ([702f498](https://github.com/Inist-CNRS/ezs/commit/702f498c675ee76085a4765acdf0ad900715932d))





## [1.4.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.4...@ezs/basics@1.4.5) (2020-09-03)

**Note:** Version bump only for package @ezs/basics





## [1.4.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.3...@ezs/basics@1.4.4) (2020-07-31)


### Bug Fixes

* missing package for [OBJStandarize] ([dcf2861](https://github.com/Inist-CNRS/ezs/commit/dcf28619e9e8187a98f380a645d2013cd69c9dcf))





## [1.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.2...@ezs/basics@1.4.3) (2020-07-27)

**Note:** Version bump only for package @ezs/basics





## [1.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.1...@ezs/basics@1.4.2) (2020-07-27)


### Bug Fixes

* 🐛 npm audit fix ([8c7a939](https://github.com/Inist-CNRS/ezs/commit/8c7a939d8a23783d470f89c6cf7ff0efc9749dea))





## [1.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.4.0...@ezs/basics@1.4.1) (2020-07-02)


### Bug Fixes

* 🐛 display status & message error ([2f67590](https://github.com/Inist-CNRS/ezs/commit/2f675904461e860eb2875513609a336209c46574))





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.3.1...@ezs/basics@1.4.0) (2020-06-26)


### Features

* 🎸 add [URLConnect] ([dfe3496](https://github.com/Inist-CNRS/ezs/commit/dfe3496e20dd20ea8fba0b810ca896b31e47b19a))





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.3.0...@ezs/basics@1.3.1) (2020-06-19)

**Note:** Version bump only for package @ezs/basics





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.6...@ezs/basics@1.3.0) (2020-06-19)


### Features

* 🎸 support root namespace ([ce7ff9f](https://github.com/Inist-CNRS/ezs/commit/ce7ff9fa93fa93de7a15664b8f1fe57894c7c50c))





## [1.2.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.5...@ezs/basics@1.2.6) (2020-05-11)

**Note:** Version bump only for package @ezs/basics





## [1.2.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.4...@ezs/basics@1.2.5) (2020-04-27)

**Note:** Version bump only for package @ezs/basics





## [1.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.3...@ezs/basics@1.2.4) (2020-04-06)


### Bug Fixes

* 🐛 security auditx ([92d7eec](https://github.com/Inist-CNRS/ezs/commit/92d7eecaad2ebd5b4ec600a33aa871c6bcf93112))





## [1.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.2...@ezs/basics@1.2.3) (2020-04-02)

**Note:** Version bump only for package @ezs/basics





## [1.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.1...@ezs/basics@1.2.2) (2020-03-30)


### Bug Fixes

* 🐛 ncu ([a05dcee](https://github.com/Inist-CNRS/ezs/commit/a05dcee3a8832a677706b8d0b30370f075785639))





## [1.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.2.0...@ezs/basics@1.2.1) (2020-02-28)

**Note:** Version bump only for package @ezs/basics





# [1.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.1.5...@ezs/basics@1.2.0) (2020-02-28)


### Features

* 🎸 new statement [INIString] ([b544681](https://github.com/Inist-CNRS/ezs/commit/b5446812a3fc16295f300e7c2bc9faaded8af6e9))





## [1.1.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.1.4...@ezs/basics@1.1.5) (2020-02-27)

**Note:** Version bump only for package @ezs/basics





## [1.1.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.1.3...@ezs/basics@1.1.4) (2020-02-26)

**Note:** Version bump only for package @ezs/basics





## [1.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.1.2...@ezs/basics@1.1.3) (2020-02-03)

**Note:** Version bump only for package @ezs/basics





## [1.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.1.1...@ezs/basics@1.1.2) (2020-01-10)

**Note:** Version bump only for package @ezs/basics





## [1.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.1.0...@ezs/basics@1.1.1) (2019-12-20)


### Bug Fixes

* **basics:** 🐛 missing package unzipper ([b04c502](https://github.com/Inist-CNRS/ezs/commit/b04c502982eea3aed745d46154bbe853a2b0c752))





# [1.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.12...@ezs/basics@1.1.0) (2019-12-13)


### Features

* 🎸 new statement ZIPExtract ([75fa47c](https://github.com/Inist-CNRS/ezs/commit/75fa47c317aa9498f248fb096feea1f722728b59))





## [1.0.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.11...@ezs/basics@1.0.12) (2019-11-28)

**Note:** Version bump only for package @ezs/basics





## [1.0.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.10...@ezs/basics@1.0.11) (2019-11-18)

**Note:** Version bump only for package @ezs/basics





## [1.0.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.9...@ezs/basics@1.0.10) (2019-11-07)

**Note:** Version bump only for package @ezs/basics





## [1.0.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.8...@ezs/basics@1.0.9) (2019-11-02)

**Note:** Version bump only for package @ezs/basics





## [1.0.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.7...@ezs/basics@1.0.8) (2019-10-25)

**Note:** Version bump only for package @ezs/basics





## [1.0.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.6...@ezs/basics@1.0.7) (2019-09-28)

**Note:** Version bump only for package @ezs/basics





## [1.0.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.5...@ezs/basics@1.0.6) (2019-09-13)

**Note:** Version bump only for package @ezs/basics





## [1.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.4...@ezs/basics@1.0.5) (2019-09-13)

**Note:** Version bump only for package @ezs/basics





## [1.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.3...@ezs/basics@1.0.4) (2019-09-11)

**Note:** Version bump only for package @ezs/basics





## [1.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.2...@ezs/basics@1.0.3) (2019-09-09)

**Note:** Version bump only for package @ezs/basics





## [1.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.1...@ezs/basics@1.0.2) (2019-09-06)

**Note:** Version bump only for package @ezs/basics





## [1.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/basics@1.0.0...@ezs/basics@1.0.1) (2019-09-06)

**Note:** Version bump only for package @ezs/basics





# 1.0.0 (2019-09-06)


### Bug Fixes

* **basics:** Fix the utils source file ([0ffb252](https://github.com/Inist-CNRS/ezs/commit/0ffb252))


### Tests

* **basics:** Use src files instead of lib ([9b669d4](https://github.com/Inist-CNRS/ezs/commit/9b669d4))


### BREAKING CHANGES

* **basics:** I want a first release at 1.0.0
