{"name": "@ezs/basics", "description": "Basics statements for EZS", "version": "2.9.2", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"JSONStream": "1.3.5", "async-retry": "1.3.3", "better-https-proxy-agent": "1.0.9", "bib2json": "0.0.1", "csv-string": "3.2.0", "debug": "4.3.3", "fetch-with-proxy": "3.0.1", "flat": "5.0.2", "from": "0.1.7", "get-stream": "6.0.1", "higher-path": "1.0.0", "lodash": "4.17.21", "make-dir": "4.0.0", "micromatch": "4.0.8", "node-abort-controller": "1.1.0", "parse-headers": "2.0.4", "path-exists": "4.0.0", "stream-write": "1.0.1", "tar-stream": "3.1.6", "tmp-filepath": "2.0.0", "unzipper": "0.10.11", "xml-mapping": "1.7.2", "xml-splitter": "1.2.1"}, "devDependencies": {"pako": "2.0.4"}, "directories": {"test": "test"}, "gitHead": "bc1be6636627b450c72d59ec404c43d87d7a42aa", "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/basics#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-basics.md --section=usage  && cp ../../docs/plugin-basics.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}