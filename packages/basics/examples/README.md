# Install

```bash
npm install -g ezs
```

## Run exemples

```bash
./csv-to-json.ezs < data/csv
./chunk-counter.ezs < data/csv
./compute.ezs < data/csv
./csv-normalize.ezs < data/csv
./csv-to-json.ezs < data/csv
./json-to-csv.ezs < data/json
./line-counter.ezs < data/csv
./rdf-splitter.ezs < data/atom
./rdf-to-csv.ezs < data/rdf
./skos-to-csv.ezs < data/skos
./xml-to-csv.ezs < data/rss
./url-fetch.ezs < data/csv
./url-merge.ezs < data/csv
```
