import assert from 'assert';
import fs from 'fs';
import ezs from '../../core/src';
import ezsBasics from '../src';

ezs.use(ezsBasics);

describe('TARExtract', () => {
    it('should extract JSON content (manual)', (done) => {
        const result = [];
        fs.createReadStream('./packages/basics/examples/data/test.tar')
            .pipe(ezs('TARExtract', { json: false }))
            .pipe(ezs.catch())
            .on('error', done)
            .on('data', (chunk) => {
                const obj = JSON.parse(chunk.value.toString());
                assert.equal(obj.test, 'ok');
                result.push(obj);
            })
            .on('end', () => {
                assert.equal(result.length, 10);
                done();
            });
    });
    it('should extract JSON content (default)', (done) => {
        const result = [];
        fs.createReadStream('./packages/basics/examples/data/test.tar')
            .pipe(ezs('TARExtract'))
            .pipe(ezs.catch())
            .on('error', done)
            .on('data', (chunk) => {
                assert.equal(chunk.test, 'ok');
                result.push(chunk);
            })
            .on('end', () => {
                assert.equal(result.length, 10);
                done();
            });
    });
    it('should extract JSON content (default) + tar.gz', (done) => {
        const result = [];
        fs.createReadStream('./packages/basics/examples/data/test.tar.gz')
            .pipe(ezs('TARExtract', { compress: true }))
            .pipe(ezs.catch())
            .on('error', done)
            .on('data', (chunk) => {
                assert.equal(chunk.test, 'ok');
                result.push(chunk);
            })
            .on('end', () => {
                assert.equal(result.length, 10);
                done();
            });
    });
    it('should extract TXT content', (done) => {
        const result = [];
        fs.createReadStream('./packages/basics/examples/data/test.tar')
            .pipe(ezs('TARExtract', { path: '**/*.txt', json: false }))
            .pipe(ezs.catch())
            .on('data', (chunk) => {
                const str = chunk.value.toString();
                assert.equal(str, 'ok\n');
                result.push(str);
            })
            .on('error', done)
            .on('end', () => {
                assert.equal(result.length, 10);
                done();
            });
    });
    it('should extract Binary content', (done) => {
        const result = [];
        fs.createReadStream('./packages/basics/examples/data/test.tar')
            .pipe(ezs('TARExtract', { path: '**/*.txt', text: false }))
            .pipe(ezs.catch())
            .on('data', (chunk) => {
                const str = chunk.value.toString();
                assert.equal(str, 'ok\n');
                result.push(str);
            })
            .on('error', done)
            .on('end', () => {
                assert.equal(result.length, 10);
                done();
            });
    });
    it('should ignore Wrong JSON files', (done) => {
        const result = [];
        fs.createReadStream('./packages/basics/examples/data/test2.tar')
            .pipe(ezs('TARExtract', { path: '**/*.json', json: true, compress: false}))
            .pipe(ezs.catch())
            .on('data', (chunk) => {
                result.push(chunk);
            })
            .on('error', done)
            .on('end', () => {
                assert.equal(result.length, 1);
                done();
            });
    });
});
