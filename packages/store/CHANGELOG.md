# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.0.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@2.0.5...@ezs/store@2.0.6) (2025-01-13)

**Note:** Version bump only for package @ezs/store





## [2.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@2.0.4...@ezs/store@2.0.5) (2024-11-22)

**Note:** Version bump only for package @ezs/store





## [2.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@2.0.3...@ezs/store@2.0.4) (2023-09-08)

**Note:** Version bump only for package @ezs/store





## [2.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@2.0.2...@ezs/store@2.0.3) (2023-07-17)

**Note:** Version bump only for package @ezs/store





## [2.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@2.0.1...@ezs/store@2.0.2) (2023-03-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [2.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@2.0.0...@ezs/store@2.0.1) (2023-01-05)

**Note:** Version bump only for package @ezs/store





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.4.4...@ezs/store@2.0.0) (2022-09-16)


### Code Refactoring

* 💡 store.close() ([34fa4c3](https://github.com/Inist-CNRS/ezs/commit/34fa4c3b9af943c7523f5dc8926567e2401ee669))


### BREAKING CHANGES

* 🧨 drop [files] [uplaod] [buffers] [bufferize]





## [1.4.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.4.3...@ezs/store@1.4.4) (2022-06-21)

**Note:** Version bump only for package @ezs/store





## [1.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.4.2...@ezs/store@1.4.3) (2022-05-24)

**Note:** Version bump only for package @ezs/store





## [1.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.4.1...@ezs/store@1.4.2) (2022-04-02)


### Bug Fixes

* 🐛 erratic error ([cf15306](https://github.com/Inist-CNRS/ezs/commit/cf15306531070939295ff8fa922795e1fd7a2fb2))





## [1.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.4.0...@ezs/store@1.4.1) (2022-04-01)


### Bug Fixes

* erratic error with store ([a26febc](https://github.com/Inist-CNRS/ezs/commit/a26febc4fe7bc0a66a7d32781dc6ef175f707f0a))





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.8...@ezs/store@1.4.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.3.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.7...@ezs/store@1.3.8) (2022-01-31)


### Bug Fixes

* avoid to delete before cleaning ([2656ffe](https://github.com/Inist-CNRS/ezs/commit/2656ffed9a89bd7dd88d4d92d48eb54b6f5e0e26))





## [1.3.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.6...@ezs/store@1.3.7) (2022-01-27)

**Note:** Version bump only for package @ezs/store





## [1.3.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.5...@ezs/store@1.3.6) (2021-12-06)

**Note:** Version bump only for package @ezs/store





## [1.3.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.4...@ezs/store@1.3.5) (2021-10-29)


### Bug Fixes

* 🐛 on lose cleanup everything ([c007409](https://github.com/Inist-CNRS/ezs/commit/c007409c1935d5d5a06ae8b0baacb6e5854b64de))
* 🐛 tests ([d376d0c](https://github.com/Inist-CNRS/ezs/commit/d376d0c631d64a66999f2bf5a4b87af4f2192a0f))





## [1.3.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.3...@ezs/store@1.3.4) (2021-10-05)

**Note:** Version bump only for package @ezs/store





## [1.3.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.2...@ezs/store@1.3.3) (2021-06-28)

**Note:** Version bump only for package @ezs/store





## [1.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.1...@ezs/store@1.3.2) (2021-06-04)

**Note:** Version bump only for package @ezs/store





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.3.0...@ezs/store@1.3.1) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.2.0...@ezs/store@1.3.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





# [1.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/store@1.1.0...@ezs/store@1.2.0) (2020-09-14)


### Features

* enable persistent DB ([210f40c](https://github.com/Inist-CNRS/ezs/commit/210f40c71fd8a43351ba1fa28298f37dc512d9fa))





# 1.1.0 (2020-07-27)


### Features

* 🎸 factorization (1store for all) ([6425981](https://github.com/Inist-CNRS/ezs/commit/6425981b5a924866e9a84aa1d5bae1e64f3a2ca5))





# Change Log
