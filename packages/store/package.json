{"name": "@ezs/store", "description": "Internal store for EZS", "version": "2.0.6", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"debug": "4.3.4", "del": "6.0.0", "leveldown": "6.1.1", "levelup": "5.1.1", "make-dir": "3.1.0", "path-exists": "4.0.0", "tmp-filepath": "2.0.0", "uuid-random": "1.3.2"}, "devDependencies": {"mkdirp": "1.0.4"}, "directories": {"test": "test"}, "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/loterre#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/store.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "echo 'No documention.'", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build"}}