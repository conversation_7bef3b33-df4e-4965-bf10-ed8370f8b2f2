# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.5.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.10...@ezs/istex@1.5.11) (2025-01-13)

**Note:** Version bump only for package @ezs/istex





## [1.5.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.9...@ezs/istex@1.5.10) (2024-11-05)

**Note:** Version bump only for package @ezs/istex





## [1.5.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.8...@ezs/istex@1.5.9) (2023-09-08)

**Note:** Version bump only for package @ezs/istex





## [1.5.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.7...@ezs/istex@1.5.8) (2023-03-28)

**Note:** Version bump only for package @ezs/istex





## [1.5.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.6...@ezs/istex@1.5.7) (2023-03-03)

**Note:** Version bump only for package @ezs/istex





## [1.5.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.5...@ezs/istex@1.5.6) (2023-01-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [1.5.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.4...@ezs/istex@1.5.5) (2023-01-25)

**Note:** Version bump only for package @ezs/istex





## [1.5.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.3...@ezs/istex@1.5.4) (2023-01-05)

**Note:** Version bump only for package @ezs/istex





## [1.5.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.2...@ezs/istex@1.5.3) (2022-09-05)

**Note:** Version bump only for package @ezs/istex





## [1.5.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.1...@ezs/istex@1.5.2) (2022-06-21)

**Note:** Version bump only for package @ezs/istex





## [1.5.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.5.0...@ezs/istex@1.5.1) (2022-03-25)

**Note:** Version bump only for package @ezs/istex





# [1.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.9...@ezs/istex@1.5.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.4.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.8...@ezs/istex@1.4.9) (2022-01-31)

**Note:** Version bump only for package @ezs/istex





## [1.4.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.7...@ezs/istex@1.4.8) (2022-01-31)

**Note:** Version bump only for package @ezs/istex





## [1.4.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.6...@ezs/istex@1.4.7) (2022-01-27)


### Bug Fixes

* 🐛 improve NUMBER transtype operation ([b33dd88](https://github.com/Inist-CNRS/ezs/commit/b33dd887ea96e36f63ee55d12f18c22894223e5b))





## [1.4.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.5...@ezs/istex@1.4.6) (2021-10-29)

**Note:** Version bump only for package @ezs/istex





## [1.4.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.4...@ezs/istex@1.4.5) (2021-10-05)


### Bug Fixes

* 🐛 fix wrong options + miss dep.x ([5b38d05](https://github.com/Inist-CNRS/ezs/commit/5b38d05199a9a49c73d264f4ddb9a45dd0e64c7e))





## [1.4.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.3...@ezs/istex@1.4.4) (2021-07-30)

**Note:** Version bump only for package @ezs/istex





## [1.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.2...@ezs/istex@1.4.3) (2021-07-22)

**Note:** Version bump only for package @ezs/istex





## [1.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.1...@ezs/istex@1.4.2) (2021-07-15)

**Note:** Version bump only for package @ezs/istex





## [1.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.4.0...@ezs/istex@1.4.1) (2021-06-28)

**Note:** Version bump only for package @ezs/istex





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.3.1...@ezs/istex@1.4.0) (2021-06-04)


### Features

* Specified Aggregation query for sub resource ([b594c95](https://github.com/Inist-CNRS/ezs/commit/b594c952b5baa57c818d62f4e9cf6d25d4bd1c7a))





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.3.0...@ezs/istex@1.3.1) (2021-05-05)


### Bug Fixes

* 🐛 remove sync code ([0a63cef](https://github.com/Inist-CNRS/ezs/commit/0a63cef73bd0ce854a8e3f0e2d2306f3ecf0b158))





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.2.4...@ezs/istex@1.3.0) (2021-04-20)


### Features

* 🎸 add [aggregateQuery] ([06c88f0](https://github.com/Inist-CNRS/ezs/commit/06c88f08d3f67b635482077322fb2c788fd8421a))





## [1.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.2.3...@ezs/istex@1.2.4) (2021-04-09)


### Bug Fixes

* 🐛 unpack crash ([326c6a0](https://github.com/Inist-CNRS/ezs/commit/326c6a0d7470703f339436da2d67cf08a50fa6db))





## [1.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.2.2...@ezs/istex@1.2.3) (2021-04-07)


### Bug Fixes

* 🐛 security patch ([37b826b](https://github.com/Inist-CNRS/ezs/commit/37b826bf8481b5fa92e00c43420037df6edebba6))





## [1.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.2.0-alpha.0...@ezs/istex@1.2.2) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))
* 🐛 remove prepatchx ([9722773](https://github.com/Inist-CNRS/ezs/commit/9722773658bab33ea76bad2a1ea74e70fc49bd51))
* **babel): fix an issue where Babel compilation leads to misleading test with 'instanceof Error', fix(istex-exchange:** fix dynamics links and reenable istex-exchange ([648e9c1](https://github.com/Inist-CNRS/ezs/commit/648e9c1964807e55650f7ff300f322ebde06cfd1))





# [1.2.0-alpha.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.7-alpha.2...@ezs/istex@1.2.0-alpha.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





## [1.1.7-alpha.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.7-alpha.1...@ezs/istex@1.1.7-alpha.2) (2020-12-22)

**Note:** Version bump only for package @ezs/istex





## [1.1.7-alpha.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.7-alpha.0...@ezs/istex@1.1.7-alpha.1) (2020-12-17)

**Note:** Version bump only for package @ezs/istex





## [1.1.7-alpha.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.6...@ezs/istex@1.1.7-alpha.0) (2020-12-04)

**Note:** Version bump only for package @ezs/istex





## [1.1.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.5...@ezs/istex@1.1.6) (2020-10-19)

**Note:** Version bump only for package @ezs/istex





## [1.1.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.4...@ezs/istex@1.1.5) (2020-10-13)


### Bug Fixes

* 🐛 [ISTEXUnzip] close the pipe with large streams ([137c1cb](https://github.com/Inist-CNRS/ezs/commit/137c1cb8a8edb87a3773556ab2e9cf10885fd4ad)), closes [#103](https://github.com/Inist-CNRS/ezs/issues/103)





## [1.1.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.3...@ezs/istex@1.1.4) (2020-09-28)


### Bug Fixes

* 🐛 security patch ([06468d5](https://github.com/Inist-CNRS/ezs/commit/06468d56d76c640fb03d7fa73f72d9cc38d44675))





## [1.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.2...@ezs/istex@1.1.3) (2020-09-17)

**Note:** Version bump only for package @ezs/istex





## [1.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.1...@ezs/istex@1.1.2) (2020-09-14)


### Bug Fixes

* 🐛 avoid UnhandledPromiseRejectionWarning ([d1c3a58](https://github.com/Inist-CNRS/ezs/commit/d1c3a58f006290b980378d026eee0f091ec7fa07))





## [1.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.1.0...@ezs/istex@1.1.1) (2020-09-08)

**Note:** Version bump only for package @ezs/istex





# [1.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.20...@ezs/istex@1.1.0) (2020-09-03)


### Bug Fixes

* Change istex-exchange dep. from local to github ([5ec6f2a](https://github.com/Inist-CNRS/ezs/commit/5ec6f2a9e30cfa771bbee6ac1b1de2f5d361dd4b))
* Change jest test environment to node, refactor(istex): give listener direct access to feed, remove getParams for getParam ([48d5f01](https://github.com/Inist-CNRS/ezs/commit/48d5f0144b2887fbe093c1dbae8694c82771c5e6))
* exclude /data/ from jest test so that we can use .js files in data mockup, test(istex): change istex-exchange to target more specificly ISTEXExchange and ISTEXToKbart, fix(istex): fix exchanger params picking ([9dbe7a1](https://github.com/Inist-CNRS/ezs/commit/9dbe7a14983b3b993ee19b9493e6e35813403504))


### Features

* **istex:** Add ISTEXExchange and ISTEXTokbart ([9ddadc2](https://github.com/Inist-CNRS/ezs/commit/9ddadc246fb7ab2ca39ec1471453f3ae8fbc6c11))





## [1.0.20](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.19...@ezs/istex@1.0.20) (2020-07-27)

**Note:** Version bump only for package @ezs/istex





## [1.0.19](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.18...@ezs/istex@1.0.19) (2020-07-27)

**Note:** Version bump only for package @ezs/istex





## [1.0.18](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.17...@ezs/istex@1.0.18) (2020-06-12)

**Note:** Version bump only for package @ezs/istex





## [1.0.17](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.16...@ezs/istex@1.0.17) (2020-05-11)

**Note:** Version bump only for package @ezs/istex





## [1.0.16](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.15...@ezs/istex@1.0.16) (2020-04-27)

**Note:** Version bump only for package @ezs/istex





## [1.0.15](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.14...@ezs/istex@1.0.15) (2020-04-06)


### Bug Fixes

* 🐛 security patch bis ([0d7fa53](https://github.com/Inist-CNRS/ezs/commit/0d7fa5303ab68ea12be77b77fd21fbb4c4fbc943))





## [1.0.14](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.13...@ezs/istex@1.0.14) (2020-02-27)

**Note:** Version bump only for package @ezs/istex





## [1.0.13](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.12...@ezs/istex@1.0.13) (2020-02-26)

**Note:** Version bump only for package @ezs/istex





## [1.0.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.11...@ezs/istex@1.0.12) (2020-02-03)

**Note:** Version bump only for package @ezs/istex





## [1.0.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.9...@ezs/istex@1.0.11) (2020-01-10)

**Note:** Version bump only for package @ezs/istex





## [1.0.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.9...@ezs/istex@1.0.10) (2020-01-10)

**Note:** Version bump only for package @ezs/istex





## [1.0.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.8...@ezs/istex@1.0.9) (2019-12-20)

**Note:** Version bump only for package @ezs/istex





## [1.0.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.7...@ezs/istex@1.0.8) (2019-12-13)

**Note:** Version bump only for package @ezs/istex





## [1.0.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.6...@ezs/istex@1.0.7) (2019-11-07)

**Note:** Version bump only for package @ezs/istex





## [1.0.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.5...@ezs/istex@1.0.6) (2019-11-02)

**Note:** Version bump only for package @ezs/istex





## [1.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.4...@ezs/istex@1.0.5) (2019-10-25)

**Note:** Version bump only for package @ezs/istex





## [1.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.3...@ezs/istex@1.0.4) (2019-09-28)

**Note:** Version bump only for package @ezs/istex





## [1.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.2...@ezs/istex@1.0.3) (2019-09-13)

**Note:** Version bump only for package @ezs/istex





## [1.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.1...@ezs/istex@1.0.2) (2019-09-13)

**Note:** Version bump only for package @ezs/istex





## [1.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/istex@1.0.0...@ezs/istex@1.0.1) (2019-09-11)


### Bug Fixes

* **istex:** Fix ISTEXParseDotCorpus in case of ids ([4cdaeba](https://github.com/Inist-CNRS/ezs/commit/4cdaeba))





# 1.0.0 (2019-09-11)


### Bug Fixes

* **istex:** Fix ISTEXScroll's output fields ([884e3df](https://github.com/Inist-CNRS/ezs/commit/884e3df))


### Build System

* **istex:** Adapt package.json ([7cee5a2](https://github.com/Inist-CNRS/ezs/commit/7cee5a2))


### BREAKING CHANGES

* **istex:** it's the first version
