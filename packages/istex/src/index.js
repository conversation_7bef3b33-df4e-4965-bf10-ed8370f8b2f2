import ISTEX from './istex';
import ISTEXResult from './istex-result';
import ISTEXFetch from './istex-fetch';
import ISTEXSave from './istex-save';
import ISTEXFiles from './files';
import ISTEXFilesContent from './files-content';
import ISTEXFilesWrap from './files-wrap';
import ISTEXParseDotCorpus from './istex-parse-dot-corpus';
import ISTEXTriplify from './istex-triplify';
import ISTEXUniq from './uniq';
import ISTEXScroll from './scroll';
import ISTEXUnzip from './unzip';
import ISTEXFacet from './facet';
import ISTEXExchange from './istex-exchange';
import ISTEXToKbart from './istex-toKbart';

const funcs = {
    ISTEX,
    ISTEXResult,
    ISTEXFetch,
    ISTEXSave,
    ISTEXFiles,
    ISTEXFilesContent,
    ISTEXFilesWrap,
    ISTEXParseDotCorpus,
    ISTEXTriplify,
    ISTEXUniq,
    ISTEXScroll,
    ISTEXUnzip,
    ISTEXFacet,
    ISTEXExchange,
    ISTEXToKbart
};

export default funcs;

module.exports = funcs;
