[use]
plugin = basics
plugin = istex

[replace]

path = Titre
value = get('title')

path = fix('Auteur(s)')
value = get('author').map('name')

path = fix('Affiliation(s)')
value = get('author').map('affiliations').filter(Boolean).map(function (b) { return b.filter(Boolean).filter(function(a) { return (a.search(/^E-mail:/) === -1 && a.search(/^Correspondence address:/) === -1) });})

path = Revue ou monographie
value = fix('n/a')

path = ISSN
value = get('host.issn').head()

path = e-ISSN
value = get('host.eissn').head()

path = ISBN
value = get('host.isbn').head()

path = e-ISBN
value = get('hos.eisbn').head()

path = Éditeur
value = fix('n/a')

path = Type de publication
value = get('genre').head()

path = Type de document
value = get('originaleGenre').head()

path = Date de publication
value = get('publicationDate')

path = fix('Langue(s) du document')
value = get('language').head()

path = Résumé
value = get('abstract')

path = Mots-clés d’auteur
value = fix('n/a')

path = Score qualité
value = get('qualityIndicators.score')

path = Version PDF
value = get('qualityIndicators.pdfVersion')

path = XML structuré
value = fix('n/a')

path = Identifiant ISTEX
value = get('id')

path = ARK
value = get('arkIstex')

path = DOI
value = get('doi').head()

path = PMID
value = get('pmid')

path = Corpus
value = get('corpusName')

path = Catégories WoS.0.Nom
value = get('categories.wos').last()

path = Catégories WoS.0.Classification
value = get('categories.wos').slice(0, -1)

path = Catégories WoS.0.Outils.0
value = multicat

path = Catégories Science-Metrix.0.Nom
value = get('categories.scienceMetrix').last()

path = Catégories Science-Metrix.0.Classification
value = get('categories.scienceMetrix').slice(0, -1)

path = Catégories Science-Metrix.0.Outils.0
value = multicat

path = Catégories Scopus.0.Nom
value = get('categories.scopus').last()

path = Catégories Scopus.0.Classification
value = get('categories.scopus').slice(0, -1)

path = Catégories Scopus.0.Outils.0
value = multicat

path = Catégories Inist.0.Nom
value = get('categories.inist').last()

path = Catégories Inist.0.Classification
value = get('categories.inist').slice(0, -1)

path = Catégories Inist.0.Outils.0
value = nb



[dump]
