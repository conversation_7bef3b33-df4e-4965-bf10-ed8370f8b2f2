mimeType = application/zip
extension = zip

# ?q=lodeux
# &extract=covers%3Bannexes%3Bfulltext%5Btei%5D%3Bmetadata%5Bmods%5D
# &size=3
# &rankBy=relevance
# &sid=istex-dl

[use]
plugin = basics
plugin = istex

[env]
path = token
value = get('token')

path = extract
value = env('extract').split(';').invokeMap(String.prototype.split, /[\[\]]/).map(_.compact).map(function(x) { return _.merge([true, ''], x)}).fromPairs().mapValues(function(x) { return _.compact(_.split(x, ','))})

[replace]
path = query
value = env('q')

# &extract=covers%3Bannexes%3Bfulltext%5Btei%5D%3Bmetadata%5Bmods%5D
[debug]

[ISTEXScroll]
sid = env('sid')
size = env('size')

[ISTEXFiles]
sid = env('sid')
metadata = env('extract.metadata')
fulltext = env('extract.fulltext')
enrichment = env('extract.enrichment')
covers = env('extract.covers')
annexes = env('extract.annexes')

[ISTEXFilesContent]
token = env('token')

#[dump]
[ISTEXFilesWrap]
format = zip

