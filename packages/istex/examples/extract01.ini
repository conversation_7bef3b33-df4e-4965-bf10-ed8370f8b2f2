[use]
plugin = basics
plugin = istex

[env]
path = ISTEX_TOKEN
value = get('ISTEX_TOKEN')

[ISTEXScroll]
query = (host.issn:"0922-6435" AND publicationDate:[1980 TO 2010]) OR (host.title:"Journal of Geophysical Research: Space Physics" AND host.issue.raw:A1)
field = title

[ISTEXFiles]
metadata = mods
metadata = xml
fulltext = tei
fulltext = pdf
enrichment = nb

[ISTEXFilesContent]
token = env('ISTEX_TOKEN')

[ISTEXFilesWrap]
format = zip

[dump]
