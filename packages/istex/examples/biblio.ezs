#!/usr/bin/env ezs

# ezs -e ./examples/biblio.ezs

[use]
plugin = basics
plugin = istex

[ISTEX]
query = *
field = arkIstex
field = pmid
field = doi
field = title
field = author
field = publicationDate
field = subject
field = host

[OBJFlatten]
safe = false
;[debug]
;text = Flatten

[ISTEXTriplify]
property = arkIstex -> http://purl.org/dc/terms/identifier
property = pmid -> http://purl.org/ontology/bibo/pmid
property = ^doi -> http://purl.org/ontology/bibo/doi
property = ^title -> http://purl.org/dc/terms/title
property = ^author/\d+/name -> http://purl.org/dc/terms/creator
property = ^author/\d+/affiliations -> https://data.istex.fr/ontology/istex#affiliation
property = ^publicationDate -> http://purl.org/dc/terms/issued
property = ^subject/\d+/value -> http://purl.org/dc/terms/subject
property = ^host/title -> https://data.istex.fr/ontology/istex#publicationTitle
property = ^host/eissn -> http://purl.org/ontology/bibo/eissn
property = ^host/issn -> http://purl.org/ontology/bibo/issn
property = ^host/isbn -> http://purl.org/ontology/bibo/isbn
property = ^host/eisbn -> http://purl.org/ontology/bibo/isbn

;property = fulltext/0/uri -> https://data.istex.fr/ontology/istex#accessURL

[ISTEXUniq]
