{"name": "@ezs/istex", "description": "ISTEX statements for EZS", "version": "1.5.11", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "contributors": [{"name": "<PERSON>", "url": "https://github.com/parmentf"}, {"name": "<PERSON>", "url": "https://github.com/touv"}], "dependencies": {"@istex/istex-exchange": "1.2.1", "archiver": "3.1.1", "async.queue": "0.5.2", "dot-prop": "5.1.1", "fetch-with-proxy": "3.0.1", "get-stream": "5.1.0", "highland": "2.13.5", "inist-ark": "2.1.3", "is-stream": "2.0.0", "qs": "6.10.3", "ramda": "0.27.1", "unzipper": "0.10.11", "write": "2.0.0"}, "directories": {"test": "test"}, "gitHead": "bc1be6636627b450c72d59ec404c43d87d7a42aa", "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/istex#readme", "keywords": ["ezs", "istex"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-istex.md --section=usage  && cp ../../docs/plugin-istex.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "preversion": "npm run doc"}}