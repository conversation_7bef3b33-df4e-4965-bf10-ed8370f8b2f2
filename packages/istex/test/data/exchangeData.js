'use strict';

module.exports = {
  "ark:/67375/8Q1-4TBMBVTV-C"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           : {
    "_id"            : "5f1e8c7add1ed2730d2b6c3f",
    "uri"            : "ark:/67375/8Q1-4TBMBVTV-C",
    "XXRn"           : "Proceedings of the Physical Society",
    "BZSn"           : "corpusName:\"iop\" AND (host.issn:\"0959-5309\" OR host.eissn:\"2051-2171\" OR host.title.raw:\"Proceedings of the Physical Society\")",
    "FdsN"           : "",
    "izmJ"           : "",
    "QFjZ"           : "refBibs.host.title.raw:\"Proceedings of the Physical Society\"",
    "BBl9"           : "",
    "Ai4O"           : "",
    "sq92"           : "P",
    "aCG7"           : "iop",
    "jN0X"           : "https://loaded-corpus.data.istex.fr/ark:/67375/XBH-4D4R3TJT-T",
    "UVFW"           : "Institute of Physics Publishing",
    "nC6e"           : "0959-5309",
    "auA7"           : "2051-2171",
    "hLNF"           : "",
    "YDZ9"           : "",
    "aqTf"           : "",
    "Rijz"           : "1926",
    "ZLPq"           : "1948",
    "kber"           : "",
    "g6FY"           : "0959-5309",
    "Fr7z"           : "P",
    "WmzM"           : "serial",
    "XX3r"           : "",
    "V7IG"           : "0959-5309",
    "publicationDate": "2020-07-27T08:12:41.668Z"
  },
  'https://api.istex.fr/document?q=corpusName%3A%22iop%22+AND+%28host.issn%3A%220959-5309%22+OR+host.eissn%3A%222051-2171%22+OR+host.title.raw%3A%22Proceedings+of+the+Physical+Society%22%29&size=0&output=&facet=host.volume%5B*-*%3A1%5D%3Ehost.publicationDate%5B*-*%3A1%5D%2Chost.issue%5B*-*%3A1%5D%3Ehost.publicationDate%5B*-*%3A1%5D&sid=istex-exchange'                                                                                                                                                                                                       : {
    total       : 4883,
    hits        : [],
    aggregations: {
      'host.volume': {
        buckets : [{
          key                   : 39,
          docCount              : 48,
          'host.publicationDate': {buckets: [], keyCount: 0},
          rangeAsString         : '[39-40['
        },
                   {
                     key                   : 40,
                     docCount              : 50,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[40-41['
                   },
                   {
                     key                   : 41,
                     docCount              : 64,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[41-42['
                   },
                   {
                     key                   : 42,
                     docCount              : 60,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[42-43['
                   },
                   {
                     key                   : 43,
                     docCount              : 51,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[43-44['
                   },
                   {
                     key                   : 44,
                     docCount              : 52,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[44-45['
                   },
                   {
                     key                   : 45,
                     docCount              : 69,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[45-46['
                   },
                   {
                     key                   : 46,
                     docCount              : 92,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[46-47['
                   },
                   {
                     key                   : 47,
                     docCount              : 96,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[47-48['
                   },
                   {
                     key                   : 48,
                     docCount              : 96,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[48-49['
                   },
                   {
                     key                   : 49,
                     docCount              : 86,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[49-50['
                   },
                   {
                     key                   : 50,
                     docCount              : 92,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[50-51['
                   },
                   {
                     key                   : 51,
                     docCount              : 95,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[51-52['
                   },
                   {
                     key                   : 52,
                     docCount              : 80,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[52-53['
                   },
                   {
                     key                   : 53,
                     docCount              : 68,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[53-54['
                   },
                   {
                     key                   : 54,
                     docCount              : 45,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[54-55['
                   },
                   {
                     key                   : 55,
                     docCount              : 56,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[55-56['
                   },
                   {
                     key                   : 56,
                     docCount              : 44,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[56-57['
                   },
                   {
                     key                   : 57,
                     docCount              : 62,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[57-58['
                   },
                   {
                     key                   : 58,
                     docCount              : 78,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[58-59['
                   },
                   {
                     key                   : 59,
                     docCount              : 103,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[59-60['
                   },
                   {
                     key                   : 60,
                     docCount              : 66,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[60-61['
                   },
                   {
                     key                   : 61,
                     docCount              : 82,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[61-62['
                   },
                   {
                     key                   : 62,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[62-63['
                   },
                   {
                     key                   : 63,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[63-64['
                   },
                   {
                     key                   : 64,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[64-65['
                   },
                   {
                     key                   : 65,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[65-66['
                   },
                   {
                     key                   : 66,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[66-67['
                   },
                   {
                     key                   : 67,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[67-68['
                   },
                   {
                     key                   : 68,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[68-69['
                   },
                   {
                     key                   : 69,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[69-70['
                   },
                   {
                     key                   : 70,
                     docCount              : 0,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[70-71['
                   },
                   {
                     key                   : 71,
                     docCount              : 144,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[71-72['
                   },
                   {
                     key                   : 72,
                     docCount              : 164,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[72-73['
                   },
                   {
                     key                   : 73,
                     docCount              : 155,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[73-74['
                   },
                   {
                     key                   : 74,
                     docCount              : 124,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[74-75['
                   },
                   {
                     key                   : 75,
                     docCount              : 141,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[75-76['
                   },
                   {
                     key                   : 76,
                     docCount              : 140,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[76-77['
                   },
                   {
                     key                   : 77,
                     docCount              : 167,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[77-78['
                   },
                   {
                     key                   : 78,
                     docCount              : 195,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[78-79['
                   },
                   {
                     key                   : 79,
                     docCount              : 167,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[79-80['
                   },
                   {
                     key                   : 80,
                     docCount              : 173,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[80-81['
                   },
                   {
                     key                   : 81,
                     docCount              : 149,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[81-82['
                   },
                   {
                     key                   : 82,
                     docCount              : 144,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[82-83['
                   },
                   {
                     key                   : 83,
                     docCount              : 127,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[83-84['
                   },
                   {
                     key                   : 84,
                     docCount              : 130,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[84-85['
                   },
                   {
                     key                   : 85,
                     docCount              : 163,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[85-86['
                   },
                   {
                     key                   : 86,
                     docCount              : 172,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[86-87['
                   },
                   {
                     key                   : 87,
                     docCount              : 129,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[87-88['
                   },
                   {
                     key                   : 88,
                     docCount              : 112,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[88-89['
                   },
                   {
                     key                   : 89,
                     docCount              : 130,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[89-90['
                   },
                   {
                     key                   : 90,
                     docCount              : 136,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[90-91['
                   },
                   {
                     key                   : 91,
                     docCount              : 133,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[91-92['
                   },
                   {
                     key                   : 92,
                     docCount              : 153,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[92-93]'
                   }],
        keyCount: 54
      },
      'host.issue' : {
        buckets : [{
          key                   : 1,
          docCount              : 1026,
          'host.publicationDate': {buckets: [], keyCount: 0},
          rangeAsString         : '[1-2['
        },
                   {
                     key                   : 2,
                     docCount              : 816,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[2-3['
                   },
                   {
                     key                   : 3,
                     docCount              : 868,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[3-4['
                   },
                   {
                     key                   : 4,
                     docCount              : 802,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[4-5['
                   },
                   {
                     key                   : 5,
                     docCount              : 718,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[5-6['
                   },
                   {
                     key                   : 6,
                     docCount              : 635,
                     'host.publicationDate': {buckets: [], keyCount: 0},
                     rangeAsString         : '[6-7]'
                   }],
        keyCount: 6
      }
    }
  },
  'https://api.istex.fr/document?q=corpusName%3A%22iop%22+AND+%28host.issn%3A%220959-5309%22+OR+host.eissn%3A%222051-2171%22+OR+host.title.raw%3A%22Proceedings+of+the+Physical+Society%22%29&size=1&output=host%2CpublicationDate%2Cauthor&facet=host.volume%5B*-*%3A1%5D%3Ehost.issue%5B*-*%3A1%5D&sid=istex-exchange'                                                                                                                                                                                                                                                : {
    total       : 4883,
    nextPageURI : 'https://api.istex.fr/document/?q=corpusName:%22iop%22%20AND%20(host.issn:%220959-5309%22%20OR%20host.eissn:%222051-2171%22%20OR%20host.title.raw:%22Proceedings%20of%20the%20Physical%20Society%22)&size=1&output=host,publicationDate,author&facet=host.volume[*-*:1]>host.issue[*-*:1]&defaultOperator=OR&from=1',
    firstPageURI: 'https://api.istex.fr/document/?q=corpusName:%22iop%22%20AND%20(host.issn:%220959-5309%22%20OR%20host.eissn:%222051-2171%22%20OR%20host.title.raw:%22Proceedings%20of%20the%20Physical%20Society%22)&size=1&output=host,publicationDate,author&facet=host.volume[*-*:1]>host.issue[*-*:1]&defaultOperator=OR&from=0',
    lastPageURI : 'https://api.istex.fr/document/?q=corpusName:%22iop%22%20AND%20(host.issn:%220959-5309%22%20OR%20host.eissn:%222051-2171%22%20OR%20host.title.raw:%22Proceedings%20of%20the%20Physical%20Society%22)&size=1&output=host,publicationDate,author&facet=host.volume[*-*:1]>host.issue[*-*:1]&defaultOperator=OR&from=4882',
    hits        : [{
      author         : [{
        name        : 'J S McPetrie',
        affiliations: ['The National Physical Laboratory']
      }],
      host           : {
        volume  : '46',
        pages   : {last: '648', first: '637'},
        issn    : ['0959-5309'],
        issue   : '5',
        genre   : ['journal'],
        language: ['unknown'],
        title   : 'Proceedings of the Physical Society'
      },
      arkIstex       : 'ark:/67375/0T8-HBDJKR80-F',
      publicationDate: '1934',
      id             : '3617F445321746AC7BD2C68C6353774E2A15168D',
      score          : 9.905408
    }],
    aggregations: {
      'host.volume': {
        buckets : [{
          key          : 39,
          docCount     : 48,
          'host.issue' : {
            buckets : [{key: 1, docCount: 48, rangeAsString: '[1-2]'}],
            keyCount: 1
          },
          rangeAsString: '[39-40['
        },
                   {
                     key          : 40,
                     docCount     : 50,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 50, rangeAsString: '[1-2]'}],
                       keyCount: 1
                     },
                     rangeAsString: '[40-41['
                   },
                   {
                     key          : 41,
                     docCount     : 64,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 64, rangeAsString: '[1-2]'}],
                       keyCount: 1
                     },
                     rangeAsString: '[41-42['
                   },
                   {
                     key          : 42,
                     docCount     : 60,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 3, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 11, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 12, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 5, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 29, rangeAsString: '[5-6]'}],
                       keyCount: 5
                     },
                     rangeAsString: '[42-43['
                   },
                   {
                     key          : 43,
                     docCount     : 51,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 11, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 9, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 9, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 6, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 16, rangeAsString: '[5-6]'}],
                       keyCount: 5
                     },
                     rangeAsString: '[43-44['
                   },
                   {
                     key          : 44,
                     docCount     : 52,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 9, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 10, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 18, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 8, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 7, rangeAsString: '[5-6]'}],
                       keyCount: 5
                     },
                     rangeAsString: '[44-45['
                   },
                   {
                     key          : 45,
                     docCount     : 69,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 12, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 15, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 12, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 12, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 10, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 8, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[45-46['
                   },
                   {
                     key          : 46,
                     docCount     : 92,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 12, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 17, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 19, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 11, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 15, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 18, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[46-47['
                   },
                   {
                     key          : 47,
                     docCount     : 96,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 13, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 15, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 14, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 19, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 23, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 12, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[47-48['
                   },
                   {
                     key          : 48,
                     docCount     : 96,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 27, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 12, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 19, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 14, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 13, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 11, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[48-49['
                   },
                   {
                     key          : 49,
                     docCount     : 86,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 7, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 11, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 14, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 12, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 16, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 8, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[49-50['
                   },
                   {
                     key          : 50,
                     docCount     : 92,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 15, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 18, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 16, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 12, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 21, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 10, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[50-51['
                   },
                   {
                     key          : 51,
                     docCount     : 95,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 17, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 15, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 19, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 15, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 16, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 13, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[51-52['
                   },
                   {
                     key          : 52,
                     docCount     : 80,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 23, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 13, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 12, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 15, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 7, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 10, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[52-53['
                   },
                   {
                     key          : 53,
                     docCount     : 68,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 7, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 13, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 12, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 12, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 12, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 12, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[53-54['
                   },
                   {
                     key          : 54,
                     docCount     : 45,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 8, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 10, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 8, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 6, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 7, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 6, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[54-55['
                   },
                   {
                     key          : 55,
                     docCount     : 56,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 8, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 7, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 10, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 10, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 15, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 6, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[55-56['
                   },
                   {
                     key          : 56,
                     docCount     : 44,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 7, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 10, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 7, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 9, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 6, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 5, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[56-57['
                   },
                   {
                     key          : 57,
                     docCount     : 62,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 11, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 8, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 9, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 10, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 9, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 15, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[57-58['
                   },
                   {
                     key          : 58,
                     docCount     : 78,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 12, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 6, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 14, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 19, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 9, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 18, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[58-59['
                   },
                   {
                     key          : 59,
                     docCount     : 103,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 21, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 14, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 15, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 20, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 16, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 17, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[59-60['
                   },
                   {
                     key          : 60,
                     docCount     : 66,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 9, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 10, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 12, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 11, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 13, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 11, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[60-61['
                   },
                   {
                     key          : 61,
                     docCount     : 82,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 16, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 12, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 10, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 12, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 14, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 18, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[61-62['
                   },
                   {
                     key          : 62,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[62-63['
                   },
                   {
                     key          : 63,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[63-64['
                   },
                   {
                     key          : 64,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[64-65['
                   },
                   {
                     key          : 65,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[65-66['
                   },
                   {
                     key          : 66,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[66-67['
                   },
                   {
                     key          : 67,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[67-68['
                   },
                   {
                     key          : 68,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[68-69['
                   },
                   {
                     key          : 69,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[69-70['
                   },
                   {
                     key          : 70,
                     docCount     : 0,
                     'host.issue' : {buckets: [], keyCount: 0},
                     rangeAsString: '[70-71['
                   },
                   {
                     key          : 71,
                     docCount     : 144,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 21, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 21, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 35, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 24, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 24, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 19, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[71-72['
                   },
                   {
                     key          : 72,
                     docCount     : 164,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 23, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 30, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 23, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 23, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 38, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 27, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[72-73['
                   },
                   {
                     key          : 73,
                     docCount     : 155,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 29, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 27, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 28, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 25, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 19, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 27, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[73-74['
                   },
                   {
                     key          : 74,
                     docCount     : 124,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 22, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 9, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 16, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 21, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 31, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 25, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[74-75['
                   },
                   {
                     key          : 75,
                     docCount     : 141,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 29, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 23, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 26, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 19, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 26, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 18, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[75-76['
                   },
                   {
                     key          : 76,
                     docCount     : 140,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 21, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 18, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 24, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 21, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 31, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 25, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[76-77['
                   },
                   {
                     key          : 77,
                     docCount     : 167,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 27, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 40, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 34, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 16, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 25, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 25, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[77-78['
                   },
                   {
                     key          : 78,
                     docCount     : 195,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 19, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 22, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 16, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 24, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 58, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 56, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[78-79['
                   },
                   {
                     key          : 79,
                     docCount     : 167,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 29, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 30, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 26, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 26, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 27, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 29, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[79-80['
                   },
                   {
                     key          : 80,
                     docCount     : 173,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 41, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 29, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 32, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 28, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 21, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 22, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[80-81['
                   },
                   {
                     key          : 81,
                     docCount     : 149,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 27, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 25, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 29, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 23, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 24, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 21, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[81-82['
                   },
                   {
                     key          : 82,
                     docCount     : 144,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 20, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 23, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 23, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 27, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 17, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 34, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[82-83['
                   },
                   {
                     key          : 83,
                     docCount     : 127,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 19, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 20, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 19, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 22, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 25, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 22, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[83-84['
                   },
                   {
                     key          : 84,
                     docCount     : 130,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 23, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 20, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 15, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 23, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 24, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 25, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[84-85['
                   },
                   {
                     key          : 85,
                     docCount     : 163,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 30, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 23, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 23, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 27, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 26, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 34, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[85-86['
                   },
                   {
                     key          : 86,
                     docCount     : 172,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 31, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 24, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 32, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 29, rangeAsString: '[4-5['},
                                  {key: 5, docCount: 28, rangeAsString: '[5-6['},
                                  {key: 6, docCount: 28, rangeAsString: '[6-7]'}],
                       keyCount: 6
                     },
                     rangeAsString: '[86-87['
                   },
                   {
                     key          : 87,
                     docCount     : 129,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 40, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 36, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 30, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 23, rangeAsString: '[4-5]'}],
                       keyCount: 4
                     },
                     rangeAsString: '[87-88['
                   },
                   {
                     key          : 88,
                     docCount     : 112,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 29, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 26, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 27, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 30, rangeAsString: '[4-5]'}],
                       keyCount: 4
                     },
                     rangeAsString: '[88-89['
                   },
                   {
                     key          : 89,
                     docCount     : 130,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 29, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 35, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 35, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 31, rangeAsString: '[4-5]'}],
                       keyCount: 4
                     },
                     rangeAsString: '[89-90['
                   },
                   {
                     key          : 90,
                     docCount     : 136,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 34, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 32, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 39, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 31, rangeAsString: '[4-5]'}],
                       keyCount: 4
                     },
                     rangeAsString: '[90-91['
                   },
                   {
                     key          : 91,
                     docCount     : 133,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 38, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 34, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 33, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 28, rangeAsString: '[4-5]'}],
                       keyCount: 4
                     },
                     rangeAsString: '[91-92['
                   },
                   {
                     key          : 92,
                     docCount     : 153,
                     'host.issue' : {
                       buckets : [{key: 1, docCount: 35, rangeAsString: '[1-2['},
                                  {key: 2, docCount: 33, rangeAsString: '[2-3['},
                                  {key: 3, docCount: 42, rangeAsString: '[3-4['},
                                  {key: 4, docCount: 43, rangeAsString: '[4-5]'}],
                       keyCount: 4
                     },
                     rangeAsString: '[92-93]'
                   }],
        keyCount: 54
      }
    }
  },
  'https://api.istex.fr/document?q=corpusName%3A%22iop%22+AND+%28host.issn%3A%220959-5309%22+OR+host.eissn%3A%222051-2171%22+OR+host.title.raw%3A%22Proceedings+of+the+Physical+Society%22%29&size=0&output=&facet=host.volume%5B*-*%3A1%5D%3EpublicationDate%5B*-*%3A1%5D%2Chost.issue%5B*-*%3A1%5D%3EpublicationDate%5B*-*%3A1%5D&sid=istex-exchange'                                                                                                                                                                                                                 : {
    total       : 4883,
    hits        : [],
    aggregations: {
      'host.volume': {
        buckets : [{
          key            : 39,
          docCount       : 48,
          publicationDate: {
            buckets : [{
              keyAsString  : '1926',
              key          : -1388534400000,
              docCount     : 48,
              rangeAsString: '[1926-1927]'
            }],
            keyCount: 1
          },
          rangeAsString  : '[39-40['
        },
                   {
                     key            : 40,
                     docCount       : 50,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1927',
                         key          : -1356998400000,
                         docCount     : 50,
                         rangeAsString: '[1927-1928]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[40-41['
                   },
                   {
                     key            : 41,
                     docCount       : 64,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1928',
                         key          : -1325462400000,
                         docCount     : 64,
                         rangeAsString: '[1928-1929]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[41-42['
                   },
                   {
                     key            : 42,
                     docCount       : 60,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1929',
                         key          : -1293840000000,
                         docCount     : 3,
                         rangeAsString: '[1929-1930['
                       },
                                  {
                                    keyAsString  : '1930',
                                    key          : -1262304000000,
                                    docCount     : 57,
                                    rangeAsString: '[1930-1931]'
                                  }],
                       keyCount: 2
                     },
                     rangeAsString  : '[42-43['
                   },
                   {
                     key            : 43,
                     docCount       : 51,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1931',
                         key          : -1230768000000,
                         docCount     : 51,
                         rangeAsString: '[1931-1932]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[43-44['
                   },
                   {
                     key            : 44,
                     docCount       : 52,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1932',
                         key          : -1199232000000,
                         docCount     : 52,
                         rangeAsString: '[1932-1933]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[44-45['
                   },
                   {
                     key            : 45,
                     docCount       : 69,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1933',
                         key          : -1167609600000,
                         docCount     : 69,
                         rangeAsString: '[1933-1934]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[45-46['
                   },
                   {
                     key            : 46,
                     docCount       : 92,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1934',
                         key          : -1136073600000,
                         docCount     : 92,
                         rangeAsString: '[1934-1935]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[46-47['
                   },
                   {
                     key            : 47,
                     docCount       : 96,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1935',
                         key          : -1104537600000,
                         docCount     : 96,
                         rangeAsString: '[1935-1936]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[47-48['
                   },
                   {
                     key            : 48,
                     docCount       : 96,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1936',
                         key          : -1073001600000,
                         docCount     : 96,
                         rangeAsString: '[1936-1937]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[48-49['
                   },
                   {
                     key            : 49,
                     docCount       : 86,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1937',
                         key          : -1041379200000,
                         docCount     : 86,
                         rangeAsString: '[1937-1938]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[49-50['
                   },
                   {
                     key            : 50,
                     docCount       : 92,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1938',
                         key          : -1009843200000,
                         docCount     : 92,
                         rangeAsString: '[1938-1939]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[50-51['
                   },
                   {
                     key            : 51,
                     docCount       : 95,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1939',
                         key          : -978307200000,
                         docCount     : 95,
                         rangeAsString: '[1939-1940]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[51-52['
                   },
                   {
                     key            : 52,
                     docCount       : 80,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1940',
                         key          : -946771200000,
                         docCount     : 80,
                         rangeAsString: '[1940-1941]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[52-53['
                   },
                   {
                     key            : 53,
                     docCount       : 68,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1941',
                         key          : -915148800000,
                         docCount     : 68,
                         rangeAsString: '[1941-1942]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[53-54['
                   },
                   {
                     key            : 54,
                     docCount       : 45,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1942',
                         key          : -883612800000,
                         docCount     : 45,
                         rangeAsString: '[1942-1943]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[54-55['
                   },
                   {
                     key            : 55,
                     docCount       : 56,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1943',
                         key          : -852076800000,
                         docCount     : 56,
                         rangeAsString: '[1943-1944]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[55-56['
                   },
                   {
                     key            : 56,
                     docCount       : 44,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1944',
                         key          : -820540800000,
                         docCount     : 44,
                         rangeAsString: '[1944-1945]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[56-57['
                   },
                   {
                     key            : 57,
                     docCount       : 62,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1945',
                         key          : -788918400000,
                         docCount     : 62,
                         rangeAsString: '[1945-1946]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[57-58['
                   },
                   {
                     key            : 58,
                     docCount       : 78,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1946',
                         key          : -757382400000,
                         docCount     : 78,
                         rangeAsString: '[1946-1947]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[58-59['
                   },
                   {
                     key            : 59,
                     docCount       : 103,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1947',
                         key          : -725846400000,
                         docCount     : 103,
                         rangeAsString: '[1947-1948]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[59-60['
                   },
                   {
                     key            : 60,
                     docCount       : 66,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1948',
                         key          : -694310400000,
                         docCount     : 66,
                         rangeAsString: '[1948-1949]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[60-61['
                   },
                   {
                     key            : 61,
                     docCount       : 82,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1948',
                         key          : -694310400000,
                         docCount     : 82,
                         rangeAsString: '[1948-1949]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[61-62['
                   },
                   {
                     key            : 62,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[62-63['
                   },
                   {
                     key            : 63,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[63-64['
                   },
                   {
                     key            : 64,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[64-65['
                   },
                   {
                     key            : 65,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[65-66['
                   },
                   {
                     key            : 66,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[66-67['
                   },
                   {
                     key            : 67,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[67-68['
                   },
                   {
                     key            : 68,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[68-69['
                   },
                   {
                     key            : 69,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[69-70['
                   },
                   {
                     key            : 70,
                     docCount       : 0,
                     publicationDate: {buckets: [], keyCount: 0},
                     rangeAsString  : '[70-71['
                   },
                   {
                     key            : 71,
                     docCount       : 144,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1958',
                         key          : -378691200000,
                         docCount     : 144,
                         rangeAsString: '[1958-1959]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[71-72['
                   },
                   {
                     key            : 72,
                     docCount       : 164,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1958',
                         key          : -378691200000,
                         docCount     : 164,
                         rangeAsString: '[1958-1959]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[72-73['
                   },
                   {
                     key            : 73,
                     docCount       : 155,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1959',
                         key          : -347155200000,
                         docCount     : 155,
                         rangeAsString: '[1959-1960]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[73-74['
                   },
                   {
                     key            : 74,
                     docCount       : 124,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1959',
                         key          : -347155200000,
                         docCount     : 124,
                         rangeAsString: '[1959-1960]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[74-75['
                   },
                   {
                     key            : 75,
                     docCount       : 141,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1960',
                         key          : -315619200000,
                         docCount     : 141,
                         rangeAsString: '[1960-1961]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[75-76['
                   },
                   {
                     key            : 76,
                     docCount       : 140,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1960',
                         key          : -315619200000,
                         docCount     : 140,
                         rangeAsString: '[1960-1961]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[76-77['
                   },
                   {
                     key            : 77,
                     docCount       : 167,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1961',
                         key          : -283996800000,
                         docCount     : 167,
                         rangeAsString: '[1961-1962]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[77-78['
                   },
                   {
                     key            : 78,
                     docCount       : 195,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1961',
                         key          : -283996800000,
                         docCount     : 195,
                         rangeAsString: '[1961-1962]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[78-79['
                   },
                   {
                     key            : 79,
                     docCount       : 167,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1962',
                         key          : -252460800000,
                         docCount     : 167,
                         rangeAsString: '[1962-1963]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[79-80['
                   },
                   {
                     key            : 80,
                     docCount       : 173,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1962',
                         key          : -252460800000,
                         docCount     : 173,
                         rangeAsString: '[1962-1963]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[80-81['
                   },
                   {
                     key            : 81,
                     docCount       : 149,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1963',
                         key          : -220924800000,
                         docCount     : 149,
                         rangeAsString: '[1963-1964]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[81-82['
                   },
                   {
                     key            : 82,
                     docCount       : 144,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1963',
                         key          : -220924800000,
                         docCount     : 144,
                         rangeAsString: '[1963-1964]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[82-83['
                   },
                   {
                     key            : 83,
                     docCount       : 127,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1964',
                         key          : -189388800000,
                         docCount     : 127,
                         rangeAsString: '[1964-1965]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[83-84['
                   },
                   {
                     key            : 84,
                     docCount       : 130,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1964',
                         key          : -189388800000,
                         docCount     : 130,
                         rangeAsString: '[1964-1965]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[84-85['
                   },
                   {
                     key            : 85,
                     docCount       : 163,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1965',
                         key          : -157766400000,
                         docCount     : 163,
                         rangeAsString: '[1965-1966]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[85-86['
                   },
                   {
                     key            : 86,
                     docCount       : 172,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1965',
                         key          : -157766400000,
                         docCount     : 172,
                         rangeAsString: '[1965-1966]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[86-87['
                   },
                   {
                     key            : 87,
                     docCount       : 129,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1966',
                         key          : -126230400000,
                         docCount     : 129,
                         rangeAsString: '[1966-1967]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[87-88['
                   },
                   {
                     key            : 88,
                     docCount       : 112,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1966',
                         key          : -126230400000,
                         docCount     : 112,
                         rangeAsString: '[1966-1967]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[88-89['
                   },
                   {
                     key            : 89,
                     docCount       : 130,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1966',
                         key          : -126230400000,
                         docCount     : 130,
                         rangeAsString: '[1966-1967]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[89-90['
                   },
                   {
                     key            : 90,
                     docCount       : 136,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1967',
                         key          : -94694400000,
                         docCount     : 136,
                         rangeAsString: '[1967-1968]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[90-91['
                   },
                   {
                     key            : 91,
                     docCount       : 133,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1967',
                         key          : -94694400000,
                         docCount     : 133,
                         rangeAsString: '[1967-1968]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[91-92['
                   },
                   {
                     key            : 92,
                     docCount       : 153,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1967',
                         key          : -94694400000,
                         docCount     : 153,
                         rangeAsString: '[1967-1968]'
                       }],
                       keyCount: 1
                     },
                     rangeAsString  : '[92-93]'
                   }],
        keyCount: 54
      },
      'host.issue' : {
        buckets : [{
          key            : 1,
          docCount       : 1026,
          publicationDate: {
            buckets : [{
              keyAsString  : '1926',
              key          : -1388534400000,
              docCount     : 48,
              rangeAsString: '[1926-1927['
            },
                       {
                         keyAsString  : '1927',
                         key          : -1356998400000,
                         docCount     : 50,
                         rangeAsString: '[1927-1928['
                       },
                       {
                         keyAsString  : '1928',
                         key          : -1325462400000,
                         docCount     : 64,
                         rangeAsString: '[1928-1929['
                       },
                       {
                         keyAsString  : '1929',
                         key          : -1293840000000,
                         docCount     : 3,
                         rangeAsString: '[1929-1930['
                       },
                       {
                         keyAsString  : '1930',
                         key          : -1262304000000,
                         docCount     : 0,
                         rangeAsString: '[1930-1931['
                       },
                       {
                         keyAsString  : '1931',
                         key          : -1230768000000,
                         docCount     : 11,
                         rangeAsString: '[1931-1932['
                       },
                       {
                         keyAsString  : '1932',
                         key          : -1199232000000,
                         docCount     : 9,
                         rangeAsString: '[1932-1933['
                       },
                       {
                         keyAsString  : '1933',
                         key          : -1167609600000,
                         docCount     : 12,
                         rangeAsString: '[1933-1934['
                       },
                       {
                         keyAsString  : '1934',
                         key          : -1136073600000,
                         docCount     : 12,
                         rangeAsString: '[1934-1935['
                       },
                       {
                         keyAsString  : '1935',
                         key          : -1104537600000,
                         docCount     : 13,
                         rangeAsString: '[1935-1936['
                       },
                       {
                         keyAsString  : '1936',
                         key          : -1073001600000,
                         docCount     : 27,
                         rangeAsString: '[1936-1937['
                       },
                       {
                         keyAsString  : '1937',
                         key          : -1041379200000,
                         docCount     : 7,
                         rangeAsString: '[1937-1938['
                       },
                       {
                         keyAsString  : '1938',
                         key          : -1009843200000,
                         docCount     : 15,
                         rangeAsString: '[1938-1939['
                       },
                       {
                         keyAsString  : '1939',
                         key          : -978307200000,
                         docCount     : 17,
                         rangeAsString: '[1939-1940['
                       },
                       {
                         keyAsString  : '1940',
                         key          : -946771200000,
                         docCount     : 23,
                         rangeAsString: '[1940-1941['
                       },
                       {
                         keyAsString  : '1941',
                         key          : -915148800000,
                         docCount     : 7,
                         rangeAsString: '[1941-1942['
                       },
                       {
                         keyAsString  : '1942',
                         key          : -883612800000,
                         docCount     : 8,
                         rangeAsString: '[1942-1943['
                       },
                       {
                         keyAsString  : '1943',
                         key          : -852076800000,
                         docCount     : 8,
                         rangeAsString: '[1943-1944['
                       },
                       {
                         keyAsString  : '1944',
                         key          : -820540800000,
                         docCount     : 7,
                         rangeAsString: '[1944-1945['
                       },
                       {
                         keyAsString  : '1945',
                         key          : -788918400000,
                         docCount     : 11,
                         rangeAsString: '[1945-1946['
                       },
                       {
                         keyAsString  : '1946',
                         key          : -757382400000,
                         docCount     : 12,
                         rangeAsString: '[1946-1947['
                       },
                       {
                         keyAsString  : '1947',
                         key          : -725846400000,
                         docCount     : 21,
                         rangeAsString: '[1947-1948['
                       },
                       {
                         keyAsString  : '1948',
                         key          : -694310400000,
                         docCount     : 25,
                         rangeAsString: '[1948-1949['
                       },
                       {
                         keyAsString  : '1949',
                         key          : -662688000000,
                         docCount     : 0,
                         rangeAsString: '[1949-1950['
                       },
                       {
                         keyAsString  : '1950',
                         key          : -631152000000,
                         docCount     : 0,
                         rangeAsString: '[1950-1951['
                       },
                       {
                         keyAsString  : '1951',
                         key          : -599616000000,
                         docCount     : 0,
                         rangeAsString: '[1951-1952['
                       },
                       {
                         keyAsString  : '1952',
                         key          : -568080000000,
                         docCount     : 0,
                         rangeAsString: '[1952-1953['
                       },
                       {
                         keyAsString  : '1953',
                         key          : -536457600000,
                         docCount     : 0,
                         rangeAsString: '[1953-1954['
                       },
                       {
                         keyAsString  : '1954',
                         key          : -504921600000,
                         docCount     : 0,
                         rangeAsString: '[1954-1955['
                       },
                       {
                         keyAsString  : '1955',
                         key          : -473385600000,
                         docCount     : 0,
                         rangeAsString: '[1955-1956['
                       },
                       {
                         keyAsString  : '1956',
                         key          : -441849600000,
                         docCount     : 0,
                         rangeAsString: '[1956-1957['
                       },
                       {
                         keyAsString  : '1957',
                         key          : -410227200000,
                         docCount     : 0,
                         rangeAsString: '[1957-1958['
                       },
                       {
                         keyAsString  : '1958',
                         key          : -378691200000,
                         docCount     : 44,
                         rangeAsString: '[1958-1959['
                       },
                       {
                         keyAsString  : '1959',
                         key          : -347155200000,
                         docCount     : 51,
                         rangeAsString: '[1959-1960['
                       },
                       {
                         keyAsString  : '1960',
                         key          : -315619200000,
                         docCount     : 50,
                         rangeAsString: '[1960-1961['
                       },
                       {
                         keyAsString  : '1961',
                         key          : -283996800000,
                         docCount     : 46,
                         rangeAsString: '[1961-1962['
                       },
                       {
                         keyAsString  : '1962',
                         key          : -252460800000,
                         docCount     : 70,
                         rangeAsString: '[1962-1963['
                       },
                       {
                         keyAsString  : '1963',
                         key          : -220924800000,
                         docCount     : 47,
                         rangeAsString: '[1963-1964['
                       },
                       {
                         keyAsString  : '1964',
                         key          : -189388800000,
                         docCount     : 42,
                         rangeAsString: '[1964-1965['
                       },
                       {
                         keyAsString  : '1965',
                         key          : -157766400000,
                         docCount     : 61,
                         rangeAsString: '[1965-1966['
                       },
                       {
                         keyAsString  : '1966',
                         key          : -126230400000,
                         docCount     : 98,
                         rangeAsString: '[1966-1967['
                       },
                       {
                         keyAsString  : '1967',
                         key          : -94694400000,
                         docCount     : 107,
                         rangeAsString: '[1967-1968]'
                       }],
            keyCount: 42
          },
          rangeAsString  : '[1-2['
        },
                   {
                     key            : 2,
                     docCount       : 816,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1930',
                         key          : -1262304000000,
                         docCount     : 11,
                         rangeAsString: '[1930-1931['
                       },
                                  {
                                    keyAsString  : '1931',
                                    key          : -1230768000000,
                                    docCount     : 9,
                                    rangeAsString: '[1931-1932['
                                  },
                                  {
                                    keyAsString  : '1932',
                                    key          : -1199232000000,
                                    docCount     : 10,
                                    rangeAsString: '[1932-1933['
                                  },
                                  {
                                    keyAsString  : '1933',
                                    key          : -1167609600000,
                                    docCount     : 15,
                                    rangeAsString: '[1933-1934['
                                  },
                                  {
                                    keyAsString  : '1934',
                                    key          : -1136073600000,
                                    docCount     : 17,
                                    rangeAsString: '[1934-1935['
                                  },
                                  {
                                    keyAsString  : '1935',
                                    key          : -1104537600000,
                                    docCount     : 15,
                                    rangeAsString: '[1935-1936['
                                  },
                                  {
                                    keyAsString  : '1936',
                                    key          : -1073001600000,
                                    docCount     : 12,
                                    rangeAsString: '[1936-1937['
                                  },
                                  {
                                    keyAsString  : '1937',
                                    key          : -1041379200000,
                                    docCount     : 11,
                                    rangeAsString: '[1937-1938['
                                  },
                                  {
                                    keyAsString  : '1938',
                                    key          : -1009843200000,
                                    docCount     : 18,
                                    rangeAsString: '[1938-1939['
                                  },
                                  {
                                    keyAsString  : '1939',
                                    key          : -978307200000,
                                    docCount     : 15,
                                    rangeAsString: '[1939-1940['
                                  },
                                  {
                                    keyAsString  : '1940',
                                    key          : -946771200000,
                                    docCount     : 13,
                                    rangeAsString: '[1940-1941['
                                  },
                                  {
                                    keyAsString  : '1941',
                                    key          : -915148800000,
                                    docCount     : 13,
                                    rangeAsString: '[1941-1942['
                                  },
                                  {
                                    keyAsString  : '1942',
                                    key          : -883612800000,
                                    docCount     : 10,
                                    rangeAsString: '[1942-1943['
                                  },
                                  {
                                    keyAsString  : '1943',
                                    key          : -852076800000,
                                    docCount     : 7,
                                    rangeAsString: '[1943-1944['
                                  },
                                  {
                                    keyAsString  : '1944',
                                    key          : -820540800000,
                                    docCount     : 10,
                                    rangeAsString: '[1944-1945['
                                  },
                                  {
                                    keyAsString  : '1945',
                                    key          : -788918400000,
                                    docCount     : 8,
                                    rangeAsString: '[1945-1946['
                                  },
                                  {
                                    keyAsString  : '1946',
                                    key          : -757382400000,
                                    docCount     : 6,
                                    rangeAsString: '[1946-1947['
                                  },
                                  {
                                    keyAsString  : '1947',
                                    key          : -725846400000,
                                    docCount     : 14,
                                    rangeAsString: '[1947-1948['
                                  },
                                  {
                                    keyAsString  : '1948',
                                    key          : -694310400000,
                                    docCount     : 22,
                                    rangeAsString: '[1948-1949['
                                  },
                                  {
                                    keyAsString  : '1949',
                                    key          : -662688000000,
                                    docCount     : 0,
                                    rangeAsString: '[1949-1950['
                                  },
                                  {
                                    keyAsString  : '1950',
                                    key          : -631152000000,
                                    docCount     : 0,
                                    rangeAsString: '[1950-1951['
                                  },
                                  {
                                    keyAsString  : '1951',
                                    key          : -599616000000,
                                    docCount     : 0,
                                    rangeAsString: '[1951-1952['
                                  },
                                  {
                                    keyAsString  : '1952',
                                    key          : -568080000000,
                                    docCount     : 0,
                                    rangeAsString: '[1952-1953['
                                  },
                                  {
                                    keyAsString  : '1953',
                                    key          : -536457600000,
                                    docCount     : 0,
                                    rangeAsString: '[1953-1954['
                                  },
                                  {
                                    keyAsString  : '1954',
                                    key          : -504921600000,
                                    docCount     : 0,
                                    rangeAsString: '[1954-1955['
                                  },
                                  {
                                    keyAsString  : '1955',
                                    key          : -473385600000,
                                    docCount     : 0,
                                    rangeAsString: '[1955-1956['
                                  },
                                  {
                                    keyAsString  : '1956',
                                    key          : -441849600000,
                                    docCount     : 0,
                                    rangeAsString: '[1956-1957['
                                  },
                                  {
                                    keyAsString  : '1957',
                                    key          : -410227200000,
                                    docCount     : 0,
                                    rangeAsString: '[1957-1958['
                                  },
                                  {
                                    keyAsString  : '1958',
                                    key          : -378691200000,
                                    docCount     : 51,
                                    rangeAsString: '[1958-1959['
                                  },
                                  {
                                    keyAsString  : '1959',
                                    key          : -347155200000,
                                    docCount     : 36,
                                    rangeAsString: '[1959-1960['
                                  },
                                  {
                                    keyAsString  : '1960',
                                    key          : -315619200000,
                                    docCount     : 41,
                                    rangeAsString: '[1960-1961['
                                  },
                                  {
                                    keyAsString  : '1961',
                                    key          : -283996800000,
                                    docCount     : 62,
                                    rangeAsString: '[1961-1962['
                                  },
                                  {
                                    keyAsString  : '1962',
                                    key          : -252460800000,
                                    docCount     : 59,
                                    rangeAsString: '[1962-1963['
                                  },
                                  {
                                    keyAsString  : '1963',
                                    key          : -220924800000,
                                    docCount     : 48,
                                    rangeAsString: '[1963-1964['
                                  },
                                  {
                                    keyAsString  : '1964',
                                    key          : -189388800000,
                                    docCount     : 40,
                                    rangeAsString: '[1964-1965['
                                  },
                                  {
                                    keyAsString  : '1965',
                                    key          : -157766400000,
                                    docCount     : 47,
                                    rangeAsString: '[1965-1966['
                                  },
                                  {
                                    keyAsString  : '1966',
                                    key          : -126230400000,
                                    docCount     : 97,
                                    rangeAsString: '[1966-1967['
                                  },
                                  {
                                    keyAsString  : '1967',
                                    key          : -94694400000,
                                    docCount     : 99,
                                    rangeAsString: '[1967-1968]'
                                  }],
                       keyCount: 38
                     },
                     rangeAsString  : '[2-3['
                   },
                   {
                     key            : 3,
                     docCount       : 868,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1930',
                         key          : -1262304000000,
                         docCount     : 12,
                         rangeAsString: '[1930-1931['
                       },
                                  {
                                    keyAsString  : '1931',
                                    key          : -1230768000000,
                                    docCount     : 9,
                                    rangeAsString: '[1931-1932['
                                  },
                                  {
                                    keyAsString  : '1932',
                                    key          : -1199232000000,
                                    docCount     : 18,
                                    rangeAsString: '[1932-1933['
                                  },
                                  {
                                    keyAsString  : '1933',
                                    key          : -1167609600000,
                                    docCount     : 12,
                                    rangeAsString: '[1933-1934['
                                  },
                                  {
                                    keyAsString  : '1934',
                                    key          : -1136073600000,
                                    docCount     : 19,
                                    rangeAsString: '[1934-1935['
                                  },
                                  {
                                    keyAsString  : '1935',
                                    key          : -1104537600000,
                                    docCount     : 14,
                                    rangeAsString: '[1935-1936['
                                  },
                                  {
                                    keyAsString  : '1936',
                                    key          : -1073001600000,
                                    docCount     : 19,
                                    rangeAsString: '[1936-1937['
                                  },
                                  {
                                    keyAsString  : '1937',
                                    key          : -1041379200000,
                                    docCount     : 14,
                                    rangeAsString: '[1937-1938['
                                  },
                                  {
                                    keyAsString  : '1938',
                                    key          : -1009843200000,
                                    docCount     : 16,
                                    rangeAsString: '[1938-1939['
                                  },
                                  {
                                    keyAsString  : '1939',
                                    key          : -978307200000,
                                    docCount     : 19,
                                    rangeAsString: '[1939-1940['
                                  },
                                  {
                                    keyAsString  : '1940',
                                    key          : -946771200000,
                                    docCount     : 12,
                                    rangeAsString: '[1940-1941['
                                  },
                                  {
                                    keyAsString  : '1941',
                                    key          : -915148800000,
                                    docCount     : 12,
                                    rangeAsString: '[1941-1942['
                                  },
                                  {
                                    keyAsString  : '1942',
                                    key          : -883612800000,
                                    docCount     : 8,
                                    rangeAsString: '[1942-1943['
                                  },
                                  {
                                    keyAsString  : '1943',
                                    key          : -852076800000,
                                    docCount     : 10,
                                    rangeAsString: '[1943-1944['
                                  },
                                  {
                                    keyAsString  : '1944',
                                    key          : -820540800000,
                                    docCount     : 7,
                                    rangeAsString: '[1944-1945['
                                  },
                                  {
                                    keyAsString  : '1945',
                                    key          : -788918400000,
                                    docCount     : 9,
                                    rangeAsString: '[1945-1946['
                                  },
                                  {
                                    keyAsString  : '1946',
                                    key          : -757382400000,
                                    docCount     : 14,
                                    rangeAsString: '[1946-1947['
                                  },
                                  {
                                    keyAsString  : '1947',
                                    key          : -725846400000,
                                    docCount     : 15,
                                    rangeAsString: '[1947-1948['
                                  },
                                  {
                                    keyAsString  : '1948',
                                    key          : -694310400000,
                                    docCount     : 22,
                                    rangeAsString: '[1948-1949['
                                  },
                                  {
                                    keyAsString  : '1949',
                                    key          : -662688000000,
                                    docCount     : 0,
                                    rangeAsString: '[1949-1950['
                                  },
                                  {
                                    keyAsString  : '1950',
                                    key          : -631152000000,
                                    docCount     : 0,
                                    rangeAsString: '[1950-1951['
                                  },
                                  {
                                    keyAsString  : '1951',
                                    key          : -599616000000,
                                    docCount     : 0,
                                    rangeAsString: '[1951-1952['
                                  },
                                  {
                                    keyAsString  : '1952',
                                    key          : -568080000000,
                                    docCount     : 0,
                                    rangeAsString: '[1952-1953['
                                  },
                                  {
                                    keyAsString  : '1953',
                                    key          : -536457600000,
                                    docCount     : 0,
                                    rangeAsString: '[1953-1954['
                                  },
                                  {
                                    keyAsString  : '1954',
                                    key          : -504921600000,
                                    docCount     : 0,
                                    rangeAsString: '[1954-1955['
                                  },
                                  {
                                    keyAsString  : '1955',
                                    key          : -473385600000,
                                    docCount     : 0,
                                    rangeAsString: '[1955-1956['
                                  },
                                  {
                                    keyAsString  : '1956',
                                    key          : -441849600000,
                                    docCount     : 0,
                                    rangeAsString: '[1956-1957['
                                  },
                                  {
                                    keyAsString  : '1957',
                                    key          : -410227200000,
                                    docCount     : 0,
                                    rangeAsString: '[1957-1958['
                                  },
                                  {
                                    keyAsString  : '1958',
                                    key          : -378691200000,
                                    docCount     : 58,
                                    rangeAsString: '[1958-1959['
                                  },
                                  {
                                    keyAsString  : '1959',
                                    key          : -347155200000,
                                    docCount     : 44,
                                    rangeAsString: '[1959-1960['
                                  },
                                  {
                                    keyAsString  : '1960',
                                    key          : -315619200000,
                                    docCount     : 50,
                                    rangeAsString: '[1960-1961['
                                  },
                                  {
                                    keyAsString  : '1961',
                                    key          : -283996800000,
                                    docCount     : 50,
                                    rangeAsString: '[1961-1962['
                                  },
                                  {
                                    keyAsString  : '1962',
                                    key          : -252460800000,
                                    docCount     : 58,
                                    rangeAsString: '[1962-1963['
                                  },
                                  {
                                    keyAsString  : '1963',
                                    key          : -220924800000,
                                    docCount     : 52,
                                    rangeAsString: '[1963-1964['
                                  },
                                  {
                                    keyAsString  : '1964',
                                    key          : -189388800000,
                                    docCount     : 34,
                                    rangeAsString: '[1964-1965['
                                  },
                                  {
                                    keyAsString  : '1965',
                                    key          : -157766400000,
                                    docCount     : 55,
                                    rangeAsString: '[1965-1966['
                                  },
                                  {
                                    keyAsString  : '1966',
                                    key          : -126230400000,
                                    docCount     : 92,
                                    rangeAsString: '[1966-1967['
                                  },
                                  {
                                    keyAsString  : '1967',
                                    key          : -94694400000,
                                    docCount     : 114,
                                    rangeAsString: '[1967-1968]'
                                  }],
                       keyCount: 38
                     },
                     rangeAsString  : '[3-4['
                   },
                   {
                     key            : 4,
                     docCount       : 802,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1930',
                         key          : -1262304000000,
                         docCount     : 5,
                         rangeAsString: '[1930-1931['
                       },
                                  {
                                    keyAsString  : '1931',
                                    key          : -1230768000000,
                                    docCount     : 6,
                                    rangeAsString: '[1931-1932['
                                  },
                                  {
                                    keyAsString  : '1932',
                                    key          : -1199232000000,
                                    docCount     : 8,
                                    rangeAsString: '[1932-1933['
                                  },
                                  {
                                    keyAsString  : '1933',
                                    key          : -1167609600000,
                                    docCount     : 12,
                                    rangeAsString: '[1933-1934['
                                  },
                                  {
                                    keyAsString  : '1934',
                                    key          : -1136073600000,
                                    docCount     : 11,
                                    rangeAsString: '[1934-1935['
                                  },
                                  {
                                    keyAsString  : '1935',
                                    key          : -1104537600000,
                                    docCount     : 19,
                                    rangeAsString: '[1935-1936['
                                  },
                                  {
                                    keyAsString  : '1936',
                                    key          : -1073001600000,
                                    docCount     : 14,
                                    rangeAsString: '[1936-1937['
                                  },
                                  {
                                    keyAsString  : '1937',
                                    key          : -1041379200000,
                                    docCount     : 12,
                                    rangeAsString: '[1937-1938['
                                  },
                                  {
                                    keyAsString  : '1938',
                                    key          : -1009843200000,
                                    docCount     : 12,
                                    rangeAsString: '[1938-1939['
                                  },
                                  {
                                    keyAsString  : '1939',
                                    key          : -978307200000,
                                    docCount     : 15,
                                    rangeAsString: '[1939-1940['
                                  },
                                  {
                                    keyAsString  : '1940',
                                    key          : -946771200000,
                                    docCount     : 15,
                                    rangeAsString: '[1940-1941['
                                  },
                                  {
                                    keyAsString  : '1941',
                                    key          : -915148800000,
                                    docCount     : 12,
                                    rangeAsString: '[1941-1942['
                                  },
                                  {
                                    keyAsString  : '1942',
                                    key          : -883612800000,
                                    docCount     : 6,
                                    rangeAsString: '[1942-1943['
                                  },
                                  {
                                    keyAsString  : '1943',
                                    key          : -852076800000,
                                    docCount     : 10,
                                    rangeAsString: '[1943-1944['
                                  },
                                  {
                                    keyAsString  : '1944',
                                    key          : -820540800000,
                                    docCount     : 9,
                                    rangeAsString: '[1944-1945['
                                  },
                                  {
                                    keyAsString  : '1945',
                                    key          : -788918400000,
                                    docCount     : 10,
                                    rangeAsString: '[1945-1946['
                                  },
                                  {
                                    keyAsString  : '1946',
                                    key          : -757382400000,
                                    docCount     : 19,
                                    rangeAsString: '[1946-1947['
                                  },
                                  {
                                    keyAsString  : '1947',
                                    key          : -725846400000,
                                    docCount     : 20,
                                    rangeAsString: '[1947-1948['
                                  },
                                  {
                                    keyAsString  : '1948',
                                    key          : -694310400000,
                                    docCount     : 23,
                                    rangeAsString: '[1948-1949['
                                  },
                                  {
                                    keyAsString  : '1949',
                                    key          : -662688000000,
                                    docCount     : 0,
                                    rangeAsString: '[1949-1950['
                                  },
                                  {
                                    keyAsString  : '1950',
                                    key          : -631152000000,
                                    docCount     : 0,
                                    rangeAsString: '[1950-1951['
                                  },
                                  {
                                    keyAsString  : '1951',
                                    key          : -599616000000,
                                    docCount     : 0,
                                    rangeAsString: '[1951-1952['
                                  },
                                  {
                                    keyAsString  : '1952',
                                    key          : -568080000000,
                                    docCount     : 0,
                                    rangeAsString: '[1952-1953['
                                  },
                                  {
                                    keyAsString  : '1953',
                                    key          : -536457600000,
                                    docCount     : 0,
                                    rangeAsString: '[1953-1954['
                                  },
                                  {
                                    keyAsString  : '1954',
                                    key          : -504921600000,
                                    docCount     : 0,
                                    rangeAsString: '[1954-1955['
                                  },
                                  {
                                    keyAsString  : '1955',
                                    key          : -473385600000,
                                    docCount     : 0,
                                    rangeAsString: '[1955-1956['
                                  },
                                  {
                                    keyAsString  : '1956',
                                    key          : -441849600000,
                                    docCount     : 0,
                                    rangeAsString: '[1956-1957['
                                  },
                                  {
                                    keyAsString  : '1957',
                                    key          : -410227200000,
                                    docCount     : 0,
                                    rangeAsString: '[1957-1958['
                                  },
                                  {
                                    keyAsString  : '1958',
                                    key          : -378691200000,
                                    docCount     : 47,
                                    rangeAsString: '[1958-1959['
                                  },
                                  {
                                    keyAsString  : '1959',
                                    key          : -347155200000,
                                    docCount     : 46,
                                    rangeAsString: '[1959-1960['
                                  },
                                  {
                                    keyAsString  : '1960',
                                    key          : -315619200000,
                                    docCount     : 40,
                                    rangeAsString: '[1960-1961['
                                  },
                                  {
                                    keyAsString  : '1961',
                                    key          : -283996800000,
                                    docCount     : 40,
                                    rangeAsString: '[1961-1962['
                                  },
                                  {
                                    keyAsString  : '1962',
                                    key          : -252460800000,
                                    docCount     : 54,
                                    rangeAsString: '[1962-1963['
                                  },
                                  {
                                    keyAsString  : '1963',
                                    key          : -220924800000,
                                    docCount     : 50,
                                    rangeAsString: '[1963-1964['
                                  },
                                  {
                                    keyAsString  : '1964',
                                    key          : -189388800000,
                                    docCount     : 45,
                                    rangeAsString: '[1964-1965['
                                  },
                                  {
                                    keyAsString  : '1965',
                                    key          : -157766400000,
                                    docCount     : 56,
                                    rangeAsString: '[1965-1966['
                                  },
                                  {
                                    keyAsString  : '1966',
                                    key          : -126230400000,
                                    docCount     : 84,
                                    rangeAsString: '[1966-1967['
                                  },
                                  {
                                    keyAsString  : '1967',
                                    key          : -94694400000,
                                    docCount     : 102,
                                    rangeAsString: '[1967-1968]'
                                  }],
                       keyCount: 38
                     },
                     rangeAsString  : '[4-5['
                   },
                   {
                     key            : 5,
                     docCount       : 718,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1930',
                         key          : -1262304000000,
                         docCount     : 29,
                         rangeAsString: '[1930-1931['
                       },
                                  {
                                    keyAsString  : '1931',
                                    key          : -1230768000000,
                                    docCount     : 16,
                                    rangeAsString: '[1931-1932['
                                  },
                                  {
                                    keyAsString  : '1932',
                                    key          : -1199232000000,
                                    docCount     : 7,
                                    rangeAsString: '[1932-1933['
                                  },
                                  {
                                    keyAsString  : '1933',
                                    key          : -1167609600000,
                                    docCount     : 10,
                                    rangeAsString: '[1933-1934['
                                  },
                                  {
                                    keyAsString  : '1934',
                                    key          : -1136073600000,
                                    docCount     : 15,
                                    rangeAsString: '[1934-1935['
                                  },
                                  {
                                    keyAsString  : '1935',
                                    key          : -1104537600000,
                                    docCount     : 23,
                                    rangeAsString: '[1935-1936['
                                  },
                                  {
                                    keyAsString  : '1936',
                                    key          : -1073001600000,
                                    docCount     : 13,
                                    rangeAsString: '[1936-1937['
                                  },
                                  {
                                    keyAsString  : '1937',
                                    key          : -1041379200000,
                                    docCount     : 16,
                                    rangeAsString: '[1937-1938['
                                  },
                                  {
                                    keyAsString  : '1938',
                                    key          : -1009843200000,
                                    docCount     : 21,
                                    rangeAsString: '[1938-1939['
                                  },
                                  {
                                    keyAsString  : '1939',
                                    key          : -978307200000,
                                    docCount     : 16,
                                    rangeAsString: '[1939-1940['
                                  },
                                  {
                                    keyAsString  : '1940',
                                    key          : -946771200000,
                                    docCount     : 7,
                                    rangeAsString: '[1940-1941['
                                  },
                                  {
                                    keyAsString  : '1941',
                                    key          : -915148800000,
                                    docCount     : 12,
                                    rangeAsString: '[1941-1942['
                                  },
                                  {
                                    keyAsString  : '1942',
                                    key          : -883612800000,
                                    docCount     : 7,
                                    rangeAsString: '[1942-1943['
                                  },
                                  {
                                    keyAsString  : '1943',
                                    key          : -852076800000,
                                    docCount     : 15,
                                    rangeAsString: '[1943-1944['
                                  },
                                  {
                                    keyAsString  : '1944',
                                    key          : -820540800000,
                                    docCount     : 6,
                                    rangeAsString: '[1944-1945['
                                  },
                                  {
                                    keyAsString  : '1945',
                                    key          : -788918400000,
                                    docCount     : 9,
                                    rangeAsString: '[1945-1946['
                                  },
                                  {
                                    keyAsString  : '1946',
                                    key          : -757382400000,
                                    docCount     : 9,
                                    rangeAsString: '[1946-1947['
                                  },
                                  {
                                    keyAsString  : '1947',
                                    key          : -725846400000,
                                    docCount     : 16,
                                    rangeAsString: '[1947-1948['
                                  },
                                  {
                                    keyAsString  : '1948',
                                    key          : -694310400000,
                                    docCount     : 27,
                                    rangeAsString: '[1948-1949['
                                  },
                                  {
                                    keyAsString  : '1949',
                                    key          : -662688000000,
                                    docCount     : 0,
                                    rangeAsString: '[1949-1950['
                                  },
                                  {
                                    keyAsString  : '1950',
                                    key          : -631152000000,
                                    docCount     : 0,
                                    rangeAsString: '[1950-1951['
                                  },
                                  {
                                    keyAsString  : '1951',
                                    key          : -599616000000,
                                    docCount     : 0,
                                    rangeAsString: '[1951-1952['
                                  },
                                  {
                                    keyAsString  : '1952',
                                    key          : -568080000000,
                                    docCount     : 0,
                                    rangeAsString: '[1952-1953['
                                  },
                                  {
                                    keyAsString  : '1953',
                                    key          : -536457600000,
                                    docCount     : 0,
                                    rangeAsString: '[1953-1954['
                                  },
                                  {
                                    keyAsString  : '1954',
                                    key          : -504921600000,
                                    docCount     : 0,
                                    rangeAsString: '[1954-1955['
                                  },
                                  {
                                    keyAsString  : '1955',
                                    key          : -473385600000,
                                    docCount     : 0,
                                    rangeAsString: '[1955-1956['
                                  },
                                  {
                                    keyAsString  : '1956',
                                    key          : -441849600000,
                                    docCount     : 0,
                                    rangeAsString: '[1956-1957['
                                  },
                                  {
                                    keyAsString  : '1957',
                                    key          : -410227200000,
                                    docCount     : 0,
                                    rangeAsString: '[1957-1958['
                                  },
                                  {
                                    keyAsString  : '1958',
                                    key          : -378691200000,
                                    docCount     : 62,
                                    rangeAsString: '[1958-1959['
                                  },
                                  {
                                    keyAsString  : '1959',
                                    key          : -347155200000,
                                    docCount     : 50,
                                    rangeAsString: '[1959-1960['
                                  },
                                  {
                                    keyAsString  : '1960',
                                    key          : -315619200000,
                                    docCount     : 57,
                                    rangeAsString: '[1960-1961['
                                  },
                                  {
                                    keyAsString  : '1961',
                                    key          : -283996800000,
                                    docCount     : 83,
                                    rangeAsString: '[1961-1962['
                                  },
                                  {
                                    keyAsString  : '1962',
                                    key          : -252460800000,
                                    docCount     : 48,
                                    rangeAsString: '[1962-1963['
                                  },
                                  {
                                    keyAsString  : '1963',
                                    key          : -220924800000,
                                    docCount     : 41,
                                    rangeAsString: '[1963-1964['
                                  },
                                  {
                                    keyAsString  : '1964',
                                    key          : -189388800000,
                                    docCount     : 49,
                                    rangeAsString: '[1964-1965['
                                  },
                                  {
                                    keyAsString  : '1965',
                                    key          : -157766400000,
                                    docCount     : 54,
                                    rangeAsString: '[1965-1966]'
                                  }],
                       keyCount: 36
                     },
                     rangeAsString  : '[5-6['
                   },
                   {
                     key            : 6,
                     docCount       : 635,
                     publicationDate: {
                       buckets : [{
                         keyAsString  : '1933',
                         key          : -1167609600000,
                         docCount     : 8,
                         rangeAsString: '[1933-1934['
                       },
                                  {
                                    keyAsString  : '1934',
                                    key          : -1136073600000,
                                    docCount     : 18,
                                    rangeAsString: '[1934-1935['
                                  },
                                  {
                                    keyAsString  : '1935',
                                    key          : -1104537600000,
                                    docCount     : 12,
                                    rangeAsString: '[1935-1936['
                                  },
                                  {
                                    keyAsString  : '1936',
                                    key          : -1073001600000,
                                    docCount     : 11,
                                    rangeAsString: '[1936-1937['
                                  },
                                  {
                                    keyAsString  : '1937',
                                    key          : -1041379200000,
                                    docCount     : 8,
                                    rangeAsString: '[1937-1938['
                                  },
                                  {
                                    keyAsString  : '1938',
                                    key          : -1009843200000,
                                    docCount     : 10,
                                    rangeAsString: '[1938-1939['
                                  },
                                  {
                                    keyAsString  : '1939',
                                    key          : -978307200000,
                                    docCount     : 13,
                                    rangeAsString: '[1939-1940['
                                  },
                                  {
                                    keyAsString  : '1940',
                                    key          : -946771200000,
                                    docCount     : 10,
                                    rangeAsString: '[1940-1941['
                                  },
                                  {
                                    keyAsString  : '1941',
                                    key          : -915148800000,
                                    docCount     : 12,
                                    rangeAsString: '[1941-1942['
                                  },
                                  {
                                    keyAsString  : '1942',
                                    key          : -883612800000,
                                    docCount     : 6,
                                    rangeAsString: '[1942-1943['
                                  },
                                  {
                                    keyAsString  : '1943',
                                    key          : -852076800000,
                                    docCount     : 6,
                                    rangeAsString: '[1943-1944['
                                  },
                                  {
                                    keyAsString  : '1944',
                                    key          : -820540800000,
                                    docCount     : 5,
                                    rangeAsString: '[1944-1945['
                                  },
                                  {
                                    keyAsString  : '1945',
                                    key          : -788918400000,
                                    docCount     : 15,
                                    rangeAsString: '[1945-1946['
                                  },
                                  {
                                    keyAsString  : '1946',
                                    key          : -757382400000,
                                    docCount     : 18,
                                    rangeAsString: '[1946-1947['
                                  },
                                  {
                                    keyAsString  : '1947',
                                    key          : -725846400000,
                                    docCount     : 17,
                                    rangeAsString: '[1947-1948['
                                  },
                                  {
                                    keyAsString  : '1948',
                                    key          : -694310400000,
                                    docCount     : 29,
                                    rangeAsString: '[1948-1949['
                                  },
                                  {
                                    keyAsString  : '1949',
                                    key          : -662688000000,
                                    docCount     : 0,
                                    rangeAsString: '[1949-1950['
                                  },
                                  {
                                    keyAsString  : '1950',
                                    key          : -631152000000,
                                    docCount     : 0,
                                    rangeAsString: '[1950-1951['
                                  },
                                  {
                                    keyAsString  : '1951',
                                    key          : -599616000000,
                                    docCount     : 0,
                                    rangeAsString: '[1951-1952['
                                  },
                                  {
                                    keyAsString  : '1952',
                                    key          : -568080000000,
                                    docCount     : 0,
                                    rangeAsString: '[1952-1953['
                                  },
                                  {
                                    keyAsString  : '1953',
                                    key          : -536457600000,
                                    docCount     : 0,
                                    rangeAsString: '[1953-1954['
                                  },
                                  {
                                    keyAsString  : '1954',
                                    key          : -504921600000,
                                    docCount     : 0,
                                    rangeAsString: '[1954-1955['
                                  },
                                  {
                                    keyAsString  : '1955',
                                    key          : -473385600000,
                                    docCount     : 0,
                                    rangeAsString: '[1955-1956['
                                  },
                                  {
                                    keyAsString  : '1956',
                                    key          : -441849600000,
                                    docCount     : 0,
                                    rangeAsString: '[1956-1957['
                                  },
                                  {
                                    keyAsString  : '1957',
                                    key          : -410227200000,
                                    docCount     : 0,
                                    rangeAsString: '[1957-1958['
                                  },
                                  {
                                    keyAsString  : '1958',
                                    key          : -378691200000,
                                    docCount     : 46,
                                    rangeAsString: '[1958-1959['
                                  },
                                  {
                                    keyAsString  : '1959',
                                    key          : -347155200000,
                                    docCount     : 52,
                                    rangeAsString: '[1959-1960['
                                  },
                                  {
                                    keyAsString  : '1960',
                                    key          : -315619200000,
                                    docCount     : 43,
                                    rangeAsString: '[1960-1961['
                                  },
                                  {
                                    keyAsString  : '1961',
                                    key          : -283996800000,
                                    docCount     : 81,
                                    rangeAsString: '[1961-1962['
                                  },
                                  {
                                    keyAsString  : '1962',
                                    key          : -252460800000,
                                    docCount     : 51,
                                    rangeAsString: '[1962-1963['
                                  },
                                  {
                                    keyAsString  : '1963',
                                    key          : -220924800000,
                                    docCount     : 55,
                                    rangeAsString: '[1963-1964['
                                  },
                                  {
                                    keyAsString  : '1964',
                                    key          : -189388800000,
                                    docCount     : 47,
                                    rangeAsString: '[1964-1965['
                                  },
                                  {
                                    keyAsString  : '1965',
                                    key          : -157766400000,
                                    docCount     : 62,
                                    rangeAsString: '[1965-1966]'
                                  }],
                       keyCount: 33
                     },
                     rangeAsString  : '[6-7]'
                   }],
        keyCount: 6
      }
    }
  },
  "ark:/67375/8Q1-7MBQK4LN-J"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           : {
    "_id"            : "5f1e8beedd1ed2730d290db5",
    "uri"            : "ark:/67375/8Q1-7MBQK4LN-J",
    "XXRn"           : "The image of both churches : after the moste wonderful and heauenly Reuelacion of Sainct Iohn the Eua[n]gelist, contayning a very frutefull exposicion or pharaphrase vpon the same, wherin it is conferred with the other scripturs, and most auctorised historyes. Compiled by Iohn Bale an exlie also in this life for the faythfull testimonie of Iesu",
    "BZSn"           : "corpusName:\"eebo\" AND title:\"The image of both churches : after the moste wonderful and heauenly Reuelacion of Sainct Iohn the Euangelist, contayning a very frutefull exposicion or pharaphrase vpon the same, wherin it is conferred with the other scripturs, and most auctorised historyes. Compiled by Iohn Bale an exlie also in this life for the faythfull testimonie of Iesu\"",
    "FdsN"           : "",
    "izmJ"           : "",
    "QFjZ"           : "refBibs.host.title.raw:\"The image of both churches : after the moste wonderful and heauenly Reuelacion of Sainct Iohn the Eua[n]gelist, contayning a very frutefull exposicion or pharaphrase vpon the same, wherin it is conferred with the other scripturs, and most auctorised historyes. Compiled by Iohn Bale an exlie also in this life for the faythfull testimonie of Iesu\"",
    "BBl9"           : "",
    "Ai4O"           : "John Bale",
    "sq92"           : "T",
    "aCG7"           : "eebo",
    "jN0X"           : "https://loaded-corpus.data.istex.fr/ark:/67375/XBH-G60FFGKH-Q",
    "UVFW"           : "Ann Arbor, Mich. : UMI, d1999-",
    "nC6e"           : "",
    "auA7"           : "",
    "hLNF"           : "",
    "YDZ9"           : "",
    "aqTf"           : "http://www.sudoc.fr/173181414",
    "Rijz"           : "",
    "ZLPq"           : "",
    "kber"           : "",
    "g6FY"           : "",
    "Fr7z"           : "P",
    "WmzM"           : "monograph",
    "XX3r"           : "",
    "V7IG"           : "173181414",
    "publicationDate": "2020-07-27T08:10:21.845Z"
  },
  "https://api.istex.fr/document?q=corpusName%3A%22eebo%22+AND+title%3A%22The+image+of+both+churches+%3A+after+the+moste+wonderful+and+heauenly+Reuelacion+of+Sainct+Iohn+the+Euangelist%2C+contayning+a+very+frutefull+exposicion+or+pharaphrase+vpon+the+same%2C+wherin+it+is+conferred+with+the+other+scripturs%2C+and+most+auctorised+historyes.+Compiled+by+Iohn+Bale+an+exlie+also+in+this+life+for+the+faythfull+testimonie+of+Iesu%22&size=1&output=host%2CpublicationDate%2Cauthor&facet=host.volume%5B*-*%3A1%5D%3Ehost.issue%5B*-*%3A1%5D&sid=istex-exchange": {
    total       : 0,
    hits        : [],
    aggregations: {'host.volume': {buckets: [], keyCount: 0}}
  }
};
