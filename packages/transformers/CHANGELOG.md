# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.4.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.6...@ezs/transformers@1.4.7) (2024-11-22)

**Note:** Version bump only for package @ezs/transformers





## [1.4.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.5...@ezs/transformers@1.4.6) (2024-11-05)

**Note:** Version bump only for package @ezs/transformers





## [1.4.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.4...@ezs/transformers@1.4.5) (2024-02-07)

**Note:** Version bump only for package @ezs/transformers





## [1.4.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.3...@ezs/transformers@1.4.4) (2023-09-08)

**Note:** Version bump only for package @ezs/transformers





## [1.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.2...@ezs/transformers@1.4.3) (2023-03-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [1.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.1...@ezs/transformers@1.4.2) (2023-01-19)


### Bug Fixes

* 🐛 DEFAULT map array like others transformersx ([fd3c22c](https://github.com/Inist-CNRS/ezs/commit/fd3c22cc9d5bfcc5bb1fb322215a059e8f7b5ac7))
* 🐛 lodex not yet support module.exports ([44bbff2](https://github.com/Inist-CNRS/ezs/commit/44bbff242222d9049985c110b9d8e330704d7880))





## [1.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.4.0...@ezs/transformers@1.4.1) (2022-06-21)

**Note:** Version bump only for package @ezs/transformers





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.11...@ezs/transformers@1.4.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.3.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.10...@ezs/transformers@1.3.11) (2022-01-31)

**Note:** Version bump only for package @ezs/transformers





## [1.3.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.9...@ezs/transformers@1.3.10) (2022-01-27)


### Bug Fixes

* 🐛 improve NUMBER transtype operation ([b33dd88](https://github.com/Inist-CNRS/ezs/commit/b33dd887ea96e36f63ee55d12f18c22894223e5b))





## [1.3.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.8...@ezs/transformers@1.3.9) (2021-11-25)

**Note:** Version bump only for package @ezs/transformers





## [1.3.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.7...@ezs/transformers@1.3.8) (2021-10-05)

**Note:** Version bump only for package @ezs/transformers





## [1.3.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.6...@ezs/transformers@1.3.7) (2021-08-27)

**Note:** Version bump only for package @ezs/transformers





## [1.3.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.5...@ezs/transformers@1.3.6) (2021-07-30)


### Bug Fixes

* **transformers:** Fix error messages ([7ade53d](https://github.com/Inist-CNRS/ezs/commit/7ade53d0725ae7c0af88e790122a046994e9434b))
* **transformers:** non string search value ([fc2edef](https://github.com/Inist-CNRS/ezs/commit/fc2edef61909dc5797cee601ed8f47f6e6a9165d))





## [1.3.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.4...@ezs/transformers@1.3.5) (2021-07-22)

**Note:** Version bump only for package @ezs/transformers





## [1.3.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.3...@ezs/transformers@1.3.4) (2021-07-15)

**Note:** Version bump only for package @ezs/transformers





## [1.3.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.2...@ezs/transformers@1.3.3) (2021-06-28)

**Note:** Version bump only for package @ezs/transformers





## [1.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.1...@ezs/transformers@1.3.2) (2021-06-04)

**Note:** Version bump only for package @ezs/transformers





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.3.0...@ezs/transformers@1.3.1) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.2.4...@ezs/transformers@1.3.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





## [1.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.2.3...@ezs/transformers@1.2.4) (2020-07-27)

**Note:** Version bump only for package @ezs/transformers





## [1.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.2.2...@ezs/transformers@1.2.3) (2020-07-27)

**Note:** Version bump only for package @ezs/transformers





## [1.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.2.1...@ezs/transformers@1.2.2) (2020-02-28)


### Bug Fixes

* 🐛 works in real life ([b5c1fbf](https://github.com/Inist-CNRS/ezs/commit/b5c1fbfc32ac2dbe871afa8ea4319da7ab647e16))





## [1.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.2.0...@ezs/transformers@1.2.1) (2020-02-27)


### Bug Fixes

* 🐛 missing metas declaration ([eaf4fab](https://github.com/Inist-CNRS/ezs/commit/eaf4fab41495df9cdd30d06c9b72c83971df68e5))





# [1.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/transformers@1.1.0...@ezs/transformers@1.2.0) (2020-02-27)


### Features

* 🎸 new operations ([046c08d](https://github.com/Inist-CNRS/ezs/commit/046c08d3c9869aa863e20f663ca8beb355088762))





# 1.1.0 (2020-02-26)


### Bug Fixes

* 🐛 all transformers are working ([0a47731](https://github.com/Inist-CNRS/ezs/commit/0a47731fa9afe323c95ca71a26d042eecde1ef50))


### Features

* 🎸 new package transformers ([eef6f1f](https://github.com/Inist-CNRS/ezs/commit/eef6f1f19adc8b26ae3990ef6c14fe7cbe280edf))
