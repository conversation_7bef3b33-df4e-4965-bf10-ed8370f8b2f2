#
# Tranforme un model lodex en script ezs
# permet de récupérer les transformations
# pour les rejouer à sans lodex
# 
# La transformatino est brute, cad
# qu'une foi le fichier généré il est necessaire
# - de supprimer la ligne contenant AUTGENERATE_URI (qui n'a de sens que dans lodex)
# - d'ajouter l'instruction :
#               [exchange]
#                value = omit('$origin')
# en fin de script poiur éviter d'avoir le champ technique $origin dans son fichier résulat
# 
# Exemple d'usage complet
# Etape 1: on convertit le model en fichier ezs
#  ezs ./convert-LODEX-model-to-ezs.ini < ./modele-lodex.json  > ./transformers.ini
# 
# Etape 2 : on converit un fichier zip (dl.istex.fr) en json (lodex)
# ezs ./zip.ini ./transformers.ini ./finalize.ini < istex-subset-2020-01-09.zip > output.json

[use]
plugin = basics
plugin = analytics

[JSONParse]

[exploding]
id = name
value = transformers

[replace]
path = get('value.operation').split('').unshift('[transformers:$').push(']').join('')
value = get('value.args').reduce((o, v) => (o[v.name] = String('fix(').concat(JSON.stringify(v.value)).concat(')') , o), {})

path = get('value.operation').split('').unshift('[transformers:$').push('].field').join('')
value = get('id')

[INIString]
