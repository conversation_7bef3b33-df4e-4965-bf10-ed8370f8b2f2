{"name": "@ezs/transformers", "description": "LODEX transformers statements for EZS", "version": "1.4.7", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"json-6": "1.0.4", "lodash": "4.17.21", "sprintf-js": "1.1.2"}, "directories": {"test": "src"}, "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/transformers#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-transformers.md --section=usage  && cp ../../docs/plugin-transformers.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}