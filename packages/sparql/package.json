{"name": "@ezs/sparql", "description": "SPARQL module for ezs", "version": "1.2.5", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "contributors": [{"name": "<PERSON>", "url": "https://github.com/apinot"}, {"name": "<PERSON>", "url": "https://github.com/parmentf"}], "dependencies": {"fetch-with-proxy": "3.0.1"}, "gitHead": "bc1be6636627b450c72d59ec404c43d87d7a42aa", "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/sparql#readme", "keywords": ["ezs", "sparql"], "license": "MIT", "main": "./lib/index.js", "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-sparql.md --section=usage && cp ../../docs/plugin-sparql.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "preversion": "npm run doc"}}