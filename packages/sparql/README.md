# sparql

Ce plugin propose une série d'instructions liée à l’usage d’un SPARQL Endpoint.

## installation

```bash
npm install @ezs/sparql
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [SPARQLDecodeQuery](#sparqldecodequery)
*   [SPARQLQuery](#sparqlquery)
*   [SPARQLToDistinct](#sparqltodistinct)

### SPARQLDecodeQuery

*   **See**: SPARQLQuery

Take a query share link from a YASGUI editor and convert it into an object
which contains the query and the endpoint. Then, it could be used by
`SPARQLQuery` instruction.

<caption>Input:</caption>

```json
{
  "linkQuery": "https://data.istex.fr/triplestore/sparql/#query=SELECT+DISTINCT+%3Fg%2C+count(*)+AS+%3Fnb+%0AWHERE+%0A%7B+%0A%09graph+%3Fg+%7B+%3Fs+%3Fp+%3Fo+%7D+%0A%7D+%0ALIMIT+3&contentTypeConstruct=text%2Fturtle&endpoint=https%3A%2F%2Fdata.istex.fr%2Fsparql%2F&outputFormat=table"
}
```

<caption>Output:</caption>

```json
{
  "query": "SELECT DISTINCT ?g, count(*) AS ?nb WHERE { graph ?g { ?s ?p ?o } } LIMIT 3",
  "endpoint": "https://data.istex.fr/sparql/"
}
```

### SPARQLQuery

Take a SPARQL query and endpoint and send in output the execution result in JSON format.

<caption>Input:</caption>

```json
{
  "query": "SELECT DISTINCT ?g, count(*) AS ?nb WHERE { graph ?g { ?s ?p ?o } } LIMIT 3",
   "endpoint": "https://data.istex.fr/sparql/"
}
```

<caption>Ouput:</caption>

```json
{ "head": { "link": [], "vars": ["g", "nb"] },
  "results": { "distinct": false, "ordered": true, "bindings": [
    { "g": {
         "type": "uri",
         "value": "http://www.openlinksw.com/schemas/virtrdf#"
       },
      "nb": {
         "type": "typed-literal",
         "datatype": "http://www.w3.org/2001/XMLSchema#integer",
         "value": "2477"
    }},
    { "g": {
         "type": "uri",
         "value": "https://bibliography.data.istex.fr/notice/graph"
       },
      "nb": {
         "type": "typed-literal",
         "datatype": "http://www.w3.org/2001/XMLSchema#integer",
         "value": "308023584"
    }},
    { "g": {
         "type": "uri",
         "value": "https://scopus-category.data.istex.fr/graph"
       },
      "nb": {
         "type": "typed-literal",
         "datatype": "http://www.w3.org/2001/XMLSchema#integer",
         "value": "2542"
    }}
  ]}
}
```

### SPARQLToDistinct

Format [SPARQLQuery](#sparqlquery) result as a LODEX routine.

The input should contain **at least two fields**:

*   the first is corresponding with the **\_id** output field
*   the second is corresponding with the **value** output field

> **Warning**: input's second field value should contain an integer

See [SPARQLQuery](#sparqlquery)

<caption>Input:</caption>

```json
{ "head": { "link": [], "vars": ["g", "nb"] },
  "results": { "distinct": false, "ordered": true, "bindings": [
    { "g": {
         "type": "uri",
         "value": "http://www.openlinksw.com/schemas/virtrdf#"
       },
      "nb": {
         "type": "typed-literal",
         "datatype": "http://www.w3.org/2001/XMLSchema#integer",
         "value": "2477"
    }},
    { "g": {
         "type": "uri",
         "value": "https://bibliography.data.istex.fr/notice/graph" },
      "nb": {
         "type": "typed-literal",
         "datatype": "http://www.w3.org/2001/XMLSchema#integer",
         "value": "308023584" }},
    { "g": {
         "type": "uri",
         "value": "https://scopus-category.data.istex.fr/graph"},
      "nb": {
         "type":
         "typed-literal",
         "datatype": "http://www.w3.org/2001/XMLSchema#integer",
         "value": "2542"
    }}
  ]}
}
```

<caption>Output:</caption>

```json
{
   "total": 3,
   "data": [{
        "_id": "http://www.openlinksw.com/schemas/virtrdf#",
        "value": 2477
      }, {
        "_id": "https://bibliography.data.istex.fr/notice/graph",
        "value": 308023584
      }, {
         "_id": "https://scopus-category.data.istex.fr/graph",
         "value": 2542
       }
   ]
}
```
