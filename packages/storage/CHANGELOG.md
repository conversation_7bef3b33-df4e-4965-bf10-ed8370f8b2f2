# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.2.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.2.4...@ezs/storage@3.2.5) (2024-11-22)

**Note:** Version bump only for package @ezs/storage





## [3.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.2.3...@ezs/storage@3.2.4) (2024-11-05)

**Note:** Version bump only for package @ezs/storage





## [3.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.2.2...@ezs/storage@3.2.3) (2024-02-07)

**Note:** Version bump only for package @ezs/storage





## [3.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.2.1...@ezs/storage@3.2.2) (2023-09-08)

**Note:** Version bump only for package @ezs/storage





## [3.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.2.0...@ezs/storage@3.2.1) (2023-07-17)

**Note:** Version bump only for package @ezs/storage





# [3.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.1.0...@ezs/storage@3.2.0) (2023-06-15)


### Features

* 🎸 introduce lru with store.js ([1f97b45](https://github.com/Inist-CNRS/ezs/commit/1f97b45948c6bec1e043cc0fea2fc1c48171ad98))





# [3.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@3.0.0...@ezs/storage@3.1.0) (2023-06-14)


### Features

* 🎸 add a note to keep the most relevant element ([1d867a9](https://github.com/Inist-CNRS/ezs/commit/1d867a963d06a5ba9bd64a46a92173f4544b32b9))





# [3.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@2.0.2...@ezs/storage@3.0.0) (2023-05-22)


### Features

* 🎸 change store engine ([10494c9](https://github.com/Inist-CNRS/ezs/commit/10494c93d2ac021dcd8c5b7929b4045f6b8b0835))


### BREAKING CHANGES

* 🧨 possibly





## [2.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@2.0.1...@ezs/storage@2.0.2) (2023-03-28)

**Note:** Version bump only for package @ezs/storage





## [2.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@2.0.0...@ezs/storage@2.0.1) (2023-03-03)


### Bug Fixes

* 🐛 edge cases ([07cfac0](https://github.com/Inist-CNRS/ezs/commit/07cfac06ce630451caf05987d37225ae5f22882d))


### Reverts

* Revert "@ezs/storage could be use wit node 12" ([224b9eb](https://github.com/Inist-CNRS/ezs/commit/224b9eb522d71df059742bf67a8a0fbdcdd8929a))
* Revert "refactor: 💡 new lmdb api" ([c83162c](https://github.com/Inist-CNRS/ezs/commit/c83162cd660037d5d4af6e7f67abd581d9ae8898))





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.7.0...@ezs/storage@2.0.0) (2023-02-08)


### Code Refactoring

* 💡 all storage plugin ([23bced2](https://github.com/Inist-CNRS/ezs/commit/23bced24cca5d380852e21630802d24ccd1d7567))


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))


### BREAKING CHANGES

* 🧨 new plugin





# [1.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.6.3...@ezs/storage@1.7.0) (2022-09-16)


### Features

* 🎸 move [identify] to core ([dea845c](https://github.com/Inist-CNRS/ezs/commit/dea845ccd16575edeab62df709ba2756e332f5c1))





## [1.6.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.6.2...@ezs/storage@1.6.3) (2022-06-21)

**Note:** Version bump only for package @ezs/storage





## [1.6.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.6.1...@ezs/storage@1.6.2) (2022-04-01)


### Bug Fixes

* erratic error with store ([a26febc](https://github.com/Inist-CNRS/ezs/commit/a26febc4fe7bc0a66a7d32781dc6ef175f707f0a))





## [1.6.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.6.0...@ezs/storage@1.6.1) (2022-03-25)

**Note:** Version bump only for package @ezs/storage





# [1.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.6...@ezs/storage@1.6.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.5.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.5...@ezs/storage@1.5.6) (2022-01-31)

**Note:** Version bump only for package @ezs/storage





## [1.5.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.4...@ezs/storage@1.5.5) (2022-01-27)

**Note:** Version bump only for package @ezs/storage





## [1.5.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.3...@ezs/storage@1.5.4) (2021-10-05)

**Note:** Version bump only for package @ezs/storage





## [1.5.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.2...@ezs/storage@1.5.3) (2021-07-30)

**Note:** Version bump only for package @ezs/storage





## [1.5.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.1...@ezs/storage@1.5.2) (2021-07-22)

**Note:** Version bump only for package @ezs/storage





## [1.5.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.5.0...@ezs/storage@1.5.1) (2021-06-28)

**Note:** Version bump only for package @ezs/storage





# [1.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.4.2...@ezs/storage@1.5.0) (2021-06-04)


### Features

* Specified Aggregation query for sub resource ([b594c95](https://github.com/Inist-CNRS/ezs/commit/b594c952b5baa57c818d62f4e9cf6d25d4bd1c7a))





## [1.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.4.1...@ezs/storage@1.4.2) (2021-04-20)


### Bug Fixes

* 🐛 compute id with object with A Null Prototype ([76679a1](https://github.com/Inist-CNRS/ezs/commit/76679a17a4bb4cd33068ece7092b2996138f2a0a))





## [1.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.4.0...@ezs/storage@1.4.1) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.3.3...@ezs/storage@1.4.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





## [1.3.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.3.2...@ezs/storage@1.3.3) (2021-01-29)

**Note:** Version bump only for package @ezs/storage





## [1.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.3.1...@ezs/storage@1.3.2) (2020-09-28)


### Bug Fixes

* 🐛 security patch ([06468d5](https://github.com/Inist-CNRS/ezs/commit/06468d56d76c640fb03d7fa73f72d9cc38d44675))





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.3.0...@ezs/storage@1.3.1) (2020-09-17)

**Note:** Version bump only for package @ezs/storage





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.2.5...@ezs/storage@1.3.0) (2020-09-17)


### Features

* 🎸 set cache delay with env var ([3d935d0](https://github.com/Inist-CNRS/ezs/commit/3d935d0d22fec73098457c16f5261b950d4c5732))





## [1.2.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.2.4...@ezs/storage@1.2.5) (2020-09-14)

**Note:** Version bump only for package @ezs/storage





## [1.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.2.3...@ezs/storage@1.2.4) (2020-09-03)

**Note:** Version bump only for package @ezs/storage





## [1.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.2.2...@ezs/storage@1.2.3) (2020-07-27)

**Note:** Version bump only for package @ezs/storage





## [1.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.2.1...@ezs/storage@1.2.2) (2020-07-27)

**Note:** Version bump only for package @ezs/storage





## [1.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.2.0...@ezs/storage@1.2.1) (2020-06-12)

**Note:** Version bump only for package @ezs/storage





# [1.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.1.10...@ezs/storage@1.2.0) (2020-05-11)


### Features

* 🎸 improve : mode / cache / cli ([8ee1993](https://github.com/Inist-CNRS/ezs/commit/8ee1993724d71b0c0fe1fae9b3929a7dcb1693c5))





## [1.1.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@1.1.9...@ezs/storage@1.1.10) (2020-04-27)

**Note:** Version bump only for package @ezs/storage





## [1.1.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.8...@ezs/storage@1.1.9) (2020-04-17)


### Bug Fixes

* 🐛 storage is stablex ([33133cb](https://github.com/Inist-CNRS/ezs/commit/33133cbcd5b492874c379eb5d8d27e6bfca45097))





## [0.1.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.7...@ezs/storage@0.1.8) (2020-04-17)


### Bug Fixes

* **storage:** wrong storage path ([01b86bc](https://github.com/Inist-CNRS/ezs/commit/01b86bc07028d084ee37d0b29d153430dbf0a5fe))





## [0.1.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.6...@ezs/storage@0.1.7) (2020-04-06)


### Bug Fixes

* 🐛 security patch bis ([0d7fa53](https://github.com/Inist-CNRS/ezs/commit/0d7fa5303ab68ea12be77b77fd21fbb4c4fbc943))





## [0.1.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.5...@ezs/storage@0.1.6) (2020-03-30)


### Bug Fixes

* 🐛 'Invalid Argument' error ([debbb07](https://github.com/Inist-CNRS/ezs/commit/debbb07f6b074cff01c5385206e92c797e2d69c6))
* 🐛 bug fixes ([277de15](https://github.com/Inist-CNRS/ezs/commit/277de15c1df537113cd5dfbe0f8a74470291770c))
* 🐛 ncu ([a05dcee](https://github.com/Inist-CNRS/ezs/commit/a05dcee3a8832a677706b8d0b30370f075785639))





## [0.1.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.4...@ezs/storage@0.1.5) (2020-03-24)

**Note:** Version bump only for package @ezs/storage





## [0.1.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.3...@ezs/storage@0.1.4) (2020-03-23)


### Performance Improvements

* ⚡️ new statement [boost] ([65fe917](https://github.com/Inist-CNRS/ezs/commit/65fe917049f6804a4f26fa3c51c72c2a3d7ee6e6))





## [0.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.2...@ezs/storage@0.1.3) (2020-02-27)

**Note:** Version bump only for package @ezs/storage





## [0.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.1...@ezs/storage@0.1.2) (2020-02-26)

**Note:** Version bump only for package @ezs/storage





## [0.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/storage@0.1.0...@ezs/storage@0.1.1) (2020-02-03)

**Note:** Version bump only for package @ezs/storage





# 0.1.0 (2020-01-10)


### Features

* 🎸 new package ! ([ce25621](https://github.com/Inist-CNRS/ezs/commit/ce256211c10601e632cf6a952fda8b1bb3bc82f0))
