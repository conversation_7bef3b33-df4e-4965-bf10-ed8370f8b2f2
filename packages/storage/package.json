{"name": "@ezs/storage", "description": "Storage statements for EZS", "version": "3.2.5", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"cacache": "^17.1.3", "lodash": "4.17.21", "lru-cache": "5.1.1", "make-dir": "3.1.0", "path-exists": "4.0.0"}, "directories": {"test": "test"}, "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/storage#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-storage.md --section=usage  && cp ../../docs/plugin-storage.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}