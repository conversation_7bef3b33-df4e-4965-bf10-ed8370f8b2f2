{"name": "@ezs/libpostal", "description": "Libpostal statements for EZS", "version": "0.3.1", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"lodash": "4.17.21", "node-postal": "1.2.0"}, "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/libpostal#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "devDependencies": {"@types/node-postal": "1.1.3"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-libpostal.md --section=usage  && cp ../../docs/plugin-libpostal.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}