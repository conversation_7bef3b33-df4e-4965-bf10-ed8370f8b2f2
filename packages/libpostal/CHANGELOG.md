# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.3.0...@ezs/libpostal@0.3.1) (2024-11-05)

**Note:** Version bump only for package @ezs/libpostal





# [0.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.2.4...@ezs/libpostal@0.3.0) (2024-04-10)


* feat(libpostal)!: unify and fix the output of each function ([0beb622](https://github.com/Inist-CNRS/ezs/commit/0beb622dc72b036e5b5a4fa060bea7f89ccdd030))
* feat(libpostal)!: unifie and fix the output ([1597ad4](https://github.com/Inist-CNRS/ezs/commit/1597ad438d9c39c49f7126c100b2258a1f6933f0))


### Features

* **libpostal:** add test for parse-address ([2c5f52f](https://github.com/Inist-CNRS/ezs/commit/2c5f52ff59b217c9da533f1f81aac17fa5537520))
* **libpostal:** add test to expand-address-with and parse-address-with ([db75ebf](https://github.com/Inist-CNRS/ezs/commit/db75ebfc1ccd2187bbe24e20658e6c8083e14a69))


### BREAKING CHANGES

* The output of each function has changed
* The output of expand-address and parse-address may change





## [0.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.2.3...@ezs/libpostal@0.2.4) (2024-02-07)

**Note:** Version bump only for package @ezs/libpostal





## [0.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.2.2...@ezs/libpostal@0.2.3) (2023-09-08)

**Note:** Version bump only for package @ezs/libpostal





## [0.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.2.1...@ezs/libpostal@0.2.2) (2023-03-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [0.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.2.0...@ezs/libpostal@0.2.1) (2022-06-21)

**Note:** Version bump only for package @ezs/libpostal





# [0.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.1.5...@ezs/libpostal@0.2.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [0.1.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.1.4...@ezs/libpostal@0.1.5) (2022-01-31)

**Note:** Version bump only for package @ezs/libpostal





## [0.1.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.1.3...@ezs/libpostal@0.1.4) (2022-01-31)

**Note:** Version bump only for package @ezs/libpostal





## [0.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.1.2...@ezs/libpostal@0.1.3) (2022-01-27)

**Note:** Version bump only for package @ezs/libpostal





## [0.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.1.1...@ezs/libpostal@0.1.2) (2022-01-19)

**Note:** Version bump only for package @ezs/libpostal





## [0.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/libpostal@0.1.0...@ezs/libpostal@0.1.1) (2022-01-06)

**Note:** Version bump only for package @ezs/libpostal





# 0.1.0 (2021-12-23)


### Features

* 🎸 add libpostal package ([bf5e89a](https://github.com/Inist-CNRS/ezs/commit/bf5e89aed5914d7b844058fc71f4046d467cad79))
