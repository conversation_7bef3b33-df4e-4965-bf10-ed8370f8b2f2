# spawn

## Présentation

Ce plugin propose une série d'instructions pour utiliser des scripts ou des programmes natifs

## installation

```bash
npm install @ezs/spawn
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [exec](#exec)

### exec

Send all received {Object} to a host command

Script:

```ini
[use]
plugin = spawn

[exec]
command = ./my/script.py
args = -o
args = -x

```

#### Parameters

*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** host command
*   `args` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** argument for command
*   `concurrency` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Number of processes to launch concurrency (optional, default `auto`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;
