[use]
plugin = basics
plugin = analytics

[TARExtract]
compress = true

# Création d'un identifiant unique pour le corpus reçu
[singleton]
[singleton/identify]
[singleton/env]
path = identifier
value = get('uri').replace('uid:/', '')

# Traitement asynchnrone de tous les items du corpus
[fork]
standalone = true

# Calcul sur le coprus
[fork/assign]
path = size
value = get('value').size()

[fork/statistics]
path = size

# Sauvegarde le résultat produit
[fork/FILESave]
location = /tmp
identifier = env('identifier')
jsonl = true

# Signal le fin du traitment via un appel à un webhook (si il a été précisé dans l'entete HTTP de la requete)
[fork/swing]
test = env('headers.x-hook').startsWith('http')
[fork/swing/replace]
path = body
value = self().pick(['size', 'atime', 'mtime', 'ctime']).set('identifier', env('identifier')).set('state', 'ready')


[fork/swing/URLFetch]
url = env('headers.x-hook')
path = body
headers = Content-Type:application/json

[fork/swing/debug]
text = webhook triggered

[fork/debug]
text = process completed


# Retour immédiat d'un seul élement indiquant comment récupérer le corpus traité quand il sera prêt
[shift]
[replace]
path = id
value = http://localhost:31976/retrieve
path = value
value = env('identifier')

[JSONString]
indent = env('indent')

