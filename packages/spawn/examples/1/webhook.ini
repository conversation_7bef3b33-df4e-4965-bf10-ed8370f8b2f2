[use]
plugin = basics
plugin = spawn

[JSONParse]
separator = *

[exec]
command = ./collect.sh
concurrency = 0

[fork]
standalone = true

[fork/exec]
command = ./compute.sh
concurrency = 0

[fork/exec]
command = ./forward.sh
concurrency = 0

[replace]
path = id
value = get('filename')
path = value
value = https://webhook.site/#!/95c85279-922d-40ab-8507-26f7fe2e129b/5becaa26-83cd-4e3b-8708-94c6314ba9e6/1

[JSONString]
indent = env('indent')

