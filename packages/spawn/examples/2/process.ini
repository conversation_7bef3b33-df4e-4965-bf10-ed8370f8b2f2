[use]
plugin = basics
plugin = analytics

[JSONParse]
separator = *

# Création d'un identifiant unique pour le corpus reçu
[singleton]
[singleton/identify]
[singleton/env]
path = identifier
value = get('uri').replace('uid:/', '')

# Traitement asynchnrone de tous les items du corpus
[fork]
standalone = true

# Calcul sur le coprus
[fork/assign]
path = size
value = get('value').size()

[fork/statistics]
path = size

# Sauvegarde le résultat produit
[fork/FILESave]
location = /tmp
identifier = env('identifier')
jsonl = true

# Signal le fin du traitment via un appel à un webhook
[fork/replace]
path = body
value = self().pick(['size', 'atime', 'mtime', 'ctime']).set('identifier', env('identifier')).set('state', 'ready')

[fork/URLFetch]
url = https://webhook.site/b9108f8c-2855-4b12-ab15-023a6a5c8bbe
path = body
headers = Content-Type:application/json

# Retour immédiat d'un seul élement indiquant que le coprus est en cours de traitement
[shift]
[replace]
path = identifier
value = env('identifier')
path = state
value = pending
path = info
value = https://webhook.site/#!/b9108f8c-2855-4b12-ab15-023a6a5c8bbe/8c9c62e1-bc3c-46f7-98dc-542d4211ffe6/1

[JSONString]
indent = env('indent')

