{"parser": "babel-es<PERSON>", "env": {"browser": true, "mocha": true, "node": true, "jest": true}, "extends": "airbnb-base", "rules": {"indent": ["error", 4], "max-len": ["error", {"code": 120, "tabWidth": 4, "ignoreUrls": true}], "no-console": ["error", {"allow": ["warn"]}], "no-underscore-dangle": ["error", {"allow": ["_id", "_error"]}], "import/no-extraneous-dependencies": "off", "no-param-reassign": ["error", {"props": false}]}}