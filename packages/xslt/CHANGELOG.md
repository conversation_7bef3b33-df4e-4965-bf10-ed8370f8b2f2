# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.3.32](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.31...@ezs/xslt@1.3.32) (2024-11-05)

**Note:** Version bump only for package @ezs/xslt





## [1.3.31](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.30...@ezs/xslt@1.3.31) (2023-09-08)

**Note:** Version bump only for package @ezs/xslt





## [1.3.30](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.29...@ezs/xslt@1.3.30) (2023-03-07)

**Note:** Version bump only for package @ezs/xslt





## [1.3.29](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.28...@ezs/xslt@1.3.29) (2023-03-07)

**Note:** Version bump only for package @ezs/xslt





## [1.3.28](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.27...@ezs/xslt@1.3.28) (2023-02-27)

**Note:** Version bump only for package @ezs/xslt





## [1.3.27](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.26...@ezs/xslt@1.3.27) (2023-02-17)

**Note:** Version bump only for package @ezs/xslt





## [1.3.26](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.25...@ezs/xslt@1.3.26) (2023-02-15)

**Note:** Version bump only for package @ezs/xslt





## [1.3.25](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.24...@ezs/xslt@1.3.25) (2023-02-08)

**Note:** Version bump only for package @ezs/xslt





## [1.3.24](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.23...@ezs/xslt@1.3.24) (2023-01-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [1.3.23](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.22...@ezs/xslt@1.3.23) (2023-01-25)

**Note:** Version bump only for package @ezs/xslt





## [1.3.22](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.21...@ezs/xslt@1.3.22) (2023-01-20)

**Note:** Version bump only for package @ezs/xslt





## [1.3.21](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.20...@ezs/xslt@1.3.21) (2023-01-13)

**Note:** Version bump only for package @ezs/xslt





## [1.3.20](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.19...@ezs/xslt@1.3.20) (2023-01-05)

**Note:** Version bump only for package @ezs/xslt





## [1.3.19](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.18...@ezs/xslt@1.3.19) (2022-12-22)

**Note:** Version bump only for package @ezs/xslt





## [1.3.18](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.17...@ezs/xslt@1.3.18) (2022-12-22)

**Note:** Version bump only for package @ezs/xslt





## [1.3.17](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.16...@ezs/xslt@1.3.17) (2022-12-22)

**Note:** Version bump only for package @ezs/xslt





## [1.3.16](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.15...@ezs/xslt@1.3.16) (2022-12-22)

**Note:** Version bump only for package @ezs/xslt





## [1.3.15](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.14...@ezs/xslt@1.3.15) (2022-12-21)

**Note:** Version bump only for package @ezs/xslt





## [1.3.14](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.13...@ezs/xslt@1.3.14) (2022-12-14)

**Note:** Version bump only for package @ezs/xslt





## [1.3.13](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.12...@ezs/xslt@1.3.13) (2022-12-02)

**Note:** Version bump only for package @ezs/xslt





## [1.3.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.11...@ezs/xslt@1.3.12) (2022-11-28)

**Note:** Version bump only for package @ezs/xslt





## [1.3.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.10...@ezs/xslt@1.3.11) (2022-09-21)

**Note:** Version bump only for package @ezs/xslt





## [1.3.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.9...@ezs/xslt@1.3.10) (2022-09-19)

**Note:** Version bump only for package @ezs/xslt





## [1.3.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.8...@ezs/xslt@1.3.9) (2022-09-16)

**Note:** Version bump only for package @ezs/xslt





## [1.3.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.7...@ezs/xslt@1.3.8) (2022-09-14)

**Note:** Version bump only for package @ezs/xslt





## [1.3.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.6...@ezs/xslt@1.3.7) (2022-09-08)

**Note:** Version bump only for package @ezs/xslt





## [1.3.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.5...@ezs/xslt@1.3.6) (2022-09-06)

**Note:** Version bump only for package @ezs/xslt





## [1.3.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.4...@ezs/xslt@1.3.5) (2022-09-04)

**Note:** Version bump only for package @ezs/xslt





## [1.3.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.3...@ezs/xslt@1.3.4) (2022-06-30)

**Note:** Version bump only for package @ezs/xslt





## [1.3.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.2...@ezs/xslt@1.3.3) (2022-06-21)

**Note:** Version bump only for package @ezs/xslt





## [1.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.1...@ezs/xslt@1.3.2) (2022-04-02)

**Note:** Version bump only for package @ezs/xslt





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.3.0...@ezs/xslt@1.3.1) (2022-03-20)

**Note:** Version bump only for package @ezs/xslt





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.2.1...@ezs/xslt@1.3.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/xslt@1.2.0...@ezs/xslt@1.2.1) (2022-02-04)

**Note:** Version bump only for package @ezs/xslt





# 1.2.0 (2022-02-02)


### Features

* 🎸 add xslt package ([1d18016](https://github.com/Inist-CNRS/ezs/commit/1d18016e85c6de2a2086c8b8e474f13e02ab8a8f))
