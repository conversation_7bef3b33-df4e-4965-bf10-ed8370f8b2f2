# `@ezs/strings`

Des intructions pour manipuler des chaînes de caratères.

## installation

```bash
npm install @ezs/core
npm install @ezs/strings
```

À mettre au début du script:

```ini
[use]
plugin = strings
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [decode](#decode)
*   [encode](#encode)
*   [inflection](#inflection)
*   [sentences](#sentences)

### decode

Decodes a given string using a provided mapping, replacing strings that
match values (to)in the mapping with their corresponding keys (from). Optionally, a
prefix and suffix can be set (they are removed too from strings).

This statement is the reverse of `encode`.

Input:

```json
[{
    "id": "1",
    "value": "Flow control based inffivesup MW wind turbine",
}, {
    "id": "2",
    "value": "Motion Characteristics of infonesupinfzerosup MW Superconducting Floating Offshore Wind Turbine",
}]
```

Script:

```ini
[decode]
path = value
from = 1
to = one
from = 5
to = five
from = 0
to = zero
prefix = inf
suffix = sup
```

Output:

```json
[{
    "id": "1",
    "value": "Flow control based 5 MW wind turbine",
}, {
    "id": "2",
    "value": "Motion Characteristics of 10 MW Superconducting Floating Offshore Wind Turbine",
}]
```

> ⚠ You must give as much `from` as `to`.

See `encode`.

#### Parameters

*   `path` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** The path of the string to be decoded, within data.
*   `from` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>** An array of strings to replace with.
*   `to` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>** An array of strings to be replaced.
*   `prefix` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** A string to be removed from the beginning of each replaced substring. (optional, default `""`)
*   `suffix` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** A string to be removed from the end of each replaced substring. (optional, default `""`)

### encode

Encodes a given string using a provided mapping, replacing characters that
match keys in the mapping with their corresponding values. Optionally, a
prefix and suffix can be added to the final result.

Input:

```json
[{
    "id": "1",
    "value": "Flow control based 5 MW wind turbine",
}, {
    "id": "2",
    "value": "Motion Characteristics of 10 MW Superconducting Floating Offshore Wind Turbine",
}]
```

Script:

```ini
[encode]
path = value
from = 1
to = one
from = 5
to = five
from = 0
to = zero
prefix = inf
suffix = sup
```

Output:

```json
[{
    "id": "1",
    "value": "Flow control based inffivesup MW wind turbine",
}, {
    "id": "2",
    "value": "Motion Characteristics of infonesupinfzerosup MW Superconducting Floating Offshore Wind Turbine",
}]
```

> ⚠ The replacements are made in the order of the `from` array. This means
> that if 1 is replaced with 2, and next 2 replaced with 3, a 1 is eventually
> replaced with 3.

> ⚠ You must give as much `from` as `to`.

See `decode` to invert the processus.

#### Parameters

*   `path` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** The path of the string to be encoded, within data.
*   `from` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>** An array of strings to replace.
*   `to` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>** An array of strings to replace with.
*   `prefix` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** A string to be added to the beginning of each replaced substring. (optional, default `""`)
*   `suffix` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** A string to be added to the end of each replaced substring. (optional, default `""`)

### inflection

Take a `String` and inflect it with or more transformers from this list
pluralize, singularize, camelize, underscore, humanize, capitalize,
dasherize, titleize, demodulize, tableize, classify, foreign\_key, ordinalize

Input:

```json
{ "id": 1, "value": "all job" }
```

Script:

```ini
[inflection]
path = value
transform = pluralize
transform = capitalize
transform = dasherize
```

Output:

```json
{ "id": 1, "value": "All-jobs" }
```

> 📗 When the path is not given, the input data is considered as a string,
> allowing to apply `inflection` on a string stream.

see <https://www.npmjs.com/package/inflection>

#### Parameters

*   `path` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to segment (optional, default `""`)
*   `transform` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** name of a transformer

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>**&#x20;

### sentences

Take a `String` and split it into an array of sentences.

Input:

```json
{ "id": 1, "value": "First sentence? Second sentence. My name is Bond, J. Bond." }
```

Output:

```json
{ "id": 1, "value": ["First sentence?", "Second sentence.", "My name is Bond, J. Bond."] }
```

> 📗 When the path is not given, the input data is considered as a string,
> allowing to apply `inflection` on a string stream.

#### Parameters

*   `path` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to segment (optional, default `""`)

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>**&#x20;
