{"name": "@ezs/strings", "version": "1.0.5", "description": "Plugin d'instructions ezs pour traiter des chaînes de caractères", "keywords": ["ezs", "strings"], "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/strings#readme", "license": "MIT", "main": "lib/index.js", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Inist-CNRS/ezs.git"}, "bugs": {"url": "https://github.com/Inist-CNRS/ezs/issues"}, "peerDependencies": {"@ezs/core": "*"}, "dependencies": {"inflection": "2.0.1", "lodash": "4.17.21"}, "scripts": {"build": "babel src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-strings.md --section=usage && cp ../../docs/plugin-strings.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "preversion": "npm run doc"}}