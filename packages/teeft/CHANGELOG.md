# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.3.1...@ezs/teeft@2.3.2) (2024-11-05)

**Note:** Version bump only for package @ezs/teeft





## [2.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.3.0...@ezs/teeft@2.3.1) (2023-09-08)

**Note:** Version bump only for package @ezs/teeft





# [2.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.2.3...@ezs/teeft@2.3.0) (2023-04-14)


### Features

* **@ezs/teeft:** Add remove-short- remove-long- remove-weird-terms ([dc2db54](https://github.com/Inist-CNRS/ezs/commit/dc2db54525c8ce374c9d90b124d40aa176ad7c04))





## [2.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.2.2...@ezs/teeft@2.2.3) (2023-03-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [2.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.2.1...@ezs/teeft@2.2.2) (2023-01-05)

**Note:** Version bump only for package @ezs/teeft





## [2.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.2.0...@ezs/teeft@2.2.1) (2022-06-21)

**Note:** Version bump only for package @ezs/teeft





# [2.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.1.3...@ezs/teeft@2.2.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [2.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.1.2...@ezs/teeft@2.1.3) (2022-01-31)

**Note:** Version bump only for package @ezs/teeft





## [2.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.1.1...@ezs/teeft@2.1.2) (2022-01-27)

**Note:** Version bump only for package @ezs/teeft





## [2.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.1.0...@ezs/teeft@2.1.1) (2021-12-16)


### Bug Fixes

* **teeft:** Make stop-words work with more than one document ([3ed6ac9](https://github.com/Inist-CNRS/ezs/commit/3ed6ac9049a7ee4e9b3df1f9da9b526fdfdc98c5))





# [2.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@2.0.0...@ezs/teeft@2.1.0) (2021-12-15)


### Features

* **teeft:** Improve French teeft ([ebc853a](https://github.com/Inist-CNRS/ezs/commit/ebc853a07623d3c4219dde0dc95d528dac8def98))





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/teeft@1.1.0...@ezs/teeft@2.0.0) (2021-12-13)


### Features

* **teeft:** Add English language to extract-terms ([bff027a](https://github.com/Inist-CNRS/ezs/commit/bff027a2f54d61131c0956d86d63f0243d38d639))
* **teeft:** Add English language to natural-tag ([e487813](https://github.com/Inist-CNRS/ezs/commit/e487813d7470cc5ed3096e192fb9be5f06be7d5a))
* **teeft:** Add English stopwords ([b0301ce](https://github.com/Inist-CNRS/ezs/commit/b0301ceddda61dddfe8a382f226319d2488ba29f))
* **teeft:** Add English to filter-tags ([41c5fc6](https://github.com/Inist-CNRS/ezs/commit/41c5fc61ed6f3b693586b58786f4129ffd5b4b10))
* **teeft:** Add English to specificity ([65e018f](https://github.com/Inist-CNRS/ezs/commit/65e018f6a9f1d7c2a24975340c9c9f45fd701eaf))
* **teeft:** Add example scripts ([bc1fcc4](https://github.com/Inist-CNRS/ezs/commit/bc1fcc4927a04581f2dbf1d349fd0b2d48e31a2f))


### BREAKING CHANGES

* **teeft:** Replace weightDictionary param with lang.
* **teeft:** Use lang instead of stopwords argument.





# 1.1.0 (2021-12-06)


### Bug Fixes

* **teeft:** Fix filter-tags input structure ([573626c](https://github.com/Inist-CNRS/ezs/commit/573626cc257ddd634a885bfe6b2feb94186dff07))
* **teeft:** Fix latest test ([9e27f2a](https://github.com/Inist-CNRS/ezs/commit/9e27f2ad8d968757b563e057d3b732169f91b6db))


### Features

* **teeft:** Add .npmignore ([ff0f202](https://github.com/Inist-CNRS/ezs/commit/ff0f2021a68d96a4ebab97cc6dbee57ef0a94eea))
* **teeft:** Add filter-mono-freq ([6a6d278](https://github.com/Inist-CNRS/ezs/commit/6a6d27831d4e4a789e23b9d43b963049c940d376))
* **teeft:** Add filter-multi-spec ([995e11d](https://github.com/Inist-CNRS/ezs/commit/995e11dad35be051a1da8082fabaf85fc5302cbb))
* **teeft:** Add filter-tag and extract-terms ([f4152b7](https://github.com/Inist-CNRS/ezs/commit/f4152b74168b13cf282fbb103220b79daeb918ad))
* **teeft:** Add natural-tag ([4c38753](https://github.com/Inist-CNRS/ezs/commit/4c387537c22a6578f7333f6dad9489b6c161da4d))
* **teeft:** Add remove-numbers ([e8362f9](https://github.com/Inist-CNRS/ezs/commit/e8362f9ff70706c2f2b85cec8d9d33e1af06cce9))
* **teeft:** Add sentence-tokenize ([546b363](https://github.com/Inist-CNRS/ezs/commit/546b363fa64f0977d486b69989cc44c8b62e731d))
* **teeft:** Add specificity ([ead4c39](https://github.com/Inist-CNRS/ezs/commit/ead4c393ce150b4df9501b25d3d2260d36b982dc))
* **teeft:** Add stop-words ([2ebd75c](https://github.com/Inist-CNRS/ezs/commit/2ebd75ce4afafff21f45ab5ce659b6dfa346078e))
* **teeft:** Add sum-up-frequencies ([68a93ff](https://github.com/Inist-CNRS/ezs/commit/68a93ffb8ad6d4e2a160a01ef85f98a148939ef4))
* **teeft:** Add TeeftGetFilesContent and TeeftListFiles ([4d9d9a2](https://github.com/Inist-CNRS/ezs/commit/4d9d9a2a97754a58c11f3f0641adea540b7c7382))
* **teeft:** Add to-lower-case ([3a1c90b](https://github.com/Inist-CNRS/ezs/commit/3a1c90b7e6d8537a6a9d2779e017bcc4377668d9))
* **teeft:** Add tokenize ([52c9b42](https://github.com/Inist-CNRS/ezs/commit/52c9b42efc6ee42b8c6bb6a9ae6ab47dc0a54aa0))
* **teeft:** Change default minFrequency for filter-mono-freq ([8f37b1c](https://github.com/Inist-CNRS/ezs/commit/8f37b1c6c5fc8aeac6a30167230cc87e3070afbf))
* **teeft:** Make get-files-content return one-level array ([4b7eaa2](https://github.com/Inist-CNRS/ezs/commit/4b7eaa2e0e8d6c97cf33e2c5445ee80d13e2e296))
* **teeft:** Make list-files return a one-level array ([a2b7e3b](https://github.com/Inist-CNRS/ezs/commit/a2b7e3b24dec775038127241d20c849725805f47))
