#!/usr/bin/env ezs

# Liste les fichiers `.txt` d'un répertoire.

# `@ezs/basics` doit être installé aussi pour que ce script fonctionne.

# echo examples/data/fr-articles | npx ezs ./examples/list-files.ini

# > Remarque: utilisez `fx` pour parcourir interactivement le JSON produit.
# > <https://github.com/antonmedv/fx>

[use]
plugin = teeft
plugin = basics

[TXTParse]

[TeeftListFiles]
pattern = *.txt

[dump]
indent = true
