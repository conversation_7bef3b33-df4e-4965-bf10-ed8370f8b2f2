#!/usr/bin/env ezs

# Sépare un texte en phrases.

# `@ezs/basics` doit être installé aussi pour que ce script fonctionne.

# echo '["examples/data/en-articles/ISKO-Maghreb-2015.txt"]' | npx ezs ./examples/en-tag.ini

# > Remarque: utilisez `fx` pour parcourir interactivement le JSON produit.
# > <https://github.com/antonmedv/fx>

[use]
plugin = teeft
plugin = basics

[JSONParse]

[TeeftGetFilesContent]

[TeeftToLowerCase]
path = content

[TeeftSentenceTokenize]

[TeeftTokenize]

[TeeftNaturalTag]
lang = en

[dump]
indent = true
