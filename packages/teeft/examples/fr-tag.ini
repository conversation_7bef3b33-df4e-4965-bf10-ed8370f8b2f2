#!/usr/bin/env ezs

# Sépare un texte en phrases.

# `@ezs/basics` doit être installé aussi pour que ce script fonctionne.

# echo '["examples/data/artificial.txt"]' | npx ezs ./examples/fr-tag.ini

# > Remarque: utilisez `fx` pour parcourir interactivement le JSON produit.
# > <https://github.com/antonmedv/fx>

[use]
plugin = teeft
plugin = basics

[JSONParse]

[TeeftGetFilesContent]

[TeeftToLowerCase]
path = content

[TeeftSentenceTokenize]

[TeeftTokenize]

[TeeftNaturalTag]
lang = fr

[dump]
indent = true
