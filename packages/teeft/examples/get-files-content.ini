#!/usr/bin/env ezs

# Affiche le contenu des fichiers dont les chemins sont passés en entrée.

# `@ezs/basics` doit être installé aussi pour que ce script fonctionne.

# echo '["examples/data/artificial.txt"]' | npx ezs ./examples/get-files-content.ini
# echo '["examples/data/artificial.txt","examples/data/fr-articles/docnum1_2013.txt"]' | npx ezs ./examples/get-files-connt.ini

[use]
plugin = teeft
plugin = basics

[JSONParse]

[TeeftGetFilesContent]

[dump]
indent = true
