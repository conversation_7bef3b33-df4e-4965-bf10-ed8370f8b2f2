#!/usr/bin/env ezs

# Applique Teeft sur un répertoire contenant des fichiers `.txt` en français.


# `@ezs/basics` doit être installé aussi pour que ce script fonctionne.

# echo examples/data/fr-articles | npx ezs ./examples/teeft-fr.ini

# Installation
# ------------

# npm i @ezs/core @ezs/teeft @ezs/basics

# > Remarque: utilisez `fx` pour parcourir interactivement le JSON produit.
# > <https://github.com/antonmedv/fx>

# > Remarque: supprimez ou commentez [TeeftListFiles] et son paramètre
# > `pattern`, puis commentez les instructions `[ignore]` et `[shift]` (ainsi
# > que la ligne `length = 1`, paramètre de `[ignore]`) pour appeler le script
# > sur un seul document:
# >     echo -n examples/data/fr-articles/PCU_AC_I2D_V0811.txt | npx ezs ./examples/teeft-fr.ini

# > Remarque: pour utiliser facilement ce script à partir d'un clone du
# > monorepo, utilisez `npm link` dans les répertoires des packages `basics` et
# > `teeft`.

[use]
plugin = teeft
plugin = basics

[TXTParse]

[TeeftListFiles]
pattern = *.txt
[TeeftGetFilesContent]

# On passe le premier
# Commenter la ligne suivante pour pouvoir le traiter
[ignore]
length = 1

# On ne garde que le premier dans ce qui reste
# Commenter la ligne suivante pour traiter tous les fichiers
[shift]

[TeeftToLowerCase]
path = content

[TeeftSentenceTokenize]
[TeeftTokenize]

[TeeftNaturalTag]
lang = fr

[TeeftExtractTerms]
lang = fr
; nounTag = NOM
; adjTag = ADJ
[TeeftFilterTags]
lang = fr
; tags = NOM
; tags = ADJ
; tags = UNK
[TeeftRemoveNumbers]
[TeeftStopWords]
lang = fr

[TeeftSumUpFrequencies]
[TeeftSpecificity]
lang = fr
sort = true

[TeeftFilterMonoFreq]
#[TeeftFilterMultiSpec]

[dump]
indent = true
