; Test for conditorScroll
; Installation:
; $ npm i @ezs/core @ezs/conditor @ezs/basics
;
; Usage
; $ npx ezs -e ./examples/scroll.ini > res.json
;
; Warning: you need to put a .env file in the current directory,
; containing the CONDITOR_TOKEN variable.

[use]
plugin = conditor
plugin = basics

[conditorScroll]
q = "source:hal" authors>affiliations>"authors.affiliations.address:*"
page_size = 1000
max_page = 25
includes = authors,xPublicationDate,sourceUid
; excludes = authors.surname,authors.forename
progress = true

[ungroup]

[JSONString]
indent = true
wrap = true
