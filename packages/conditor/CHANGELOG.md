# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.13.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.13.3...@ezs/conditor@2.13.4) (2025-05-09)

**Note:** Version bump only for package @ezs/conditor





## [2.13.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.13.2...@ezs/conditor@2.13.3) (2025-02-03)

**Note:** Version bump only for package @ezs/conditor





## [2.13.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.13.1...@ezs/conditor@2.13.2) (2025-01-13)

**Note:** Version bump only for package @ezs/conditor





## [2.13.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.13.0...@ezs/conditor@2.13.1) (2024-11-29)


### Bug Fixes

* 🐛 loop lock ([81337b9](https://github.com/Inist-CNRS/ezs/commit/81337b92d431209c14ed1303561b74530c8674c7))





# [2.13.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.12.4...@ezs/conditor@2.13.0) (2024-11-29)


### Features

* 🎸 add [OAFetch] ([be91c80](https://github.com/Inist-CNRS/ezs/commit/be91c80e6a631d9ce9ef0a50ebb731e922eb1c33))





## [2.12.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.12.3...@ezs/conditor@2.12.4) (2024-11-22)

**Note:** Version bump only for package @ezs/conditor





## [2.12.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.12.2...@ezs/conditor@2.12.3) (2024-11-05)

**Note:** Version bump only for package @ezs/conditor





## [2.12.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.12.1...@ezs/conditor@2.12.2) (2024-08-02)

**Note:** Version bump only for package @ezs/conditor





## [2.12.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.12.0...@ezs/conditor@2.12.1) (2024-07-04)

**Note:** Version bump only for package @ezs/conditor





# [2.12.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.11.2...@ezs/conditor@2.12.0) (2024-05-30)


### Features

* **conditor:** Make 2023 version of RNSR the default one ([c635f9c](https://github.com/Inist-CNRS/ezs/commit/c635f9cc0ce765eb462a4dbacc057e09964cf99e))





## [2.11.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.11.1...@ezs/conditor@2.11.2) (2024-05-29)

**Note:** Version bump only for package @ezs/conditor





## [2.11.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.11.0...@ezs/conditor@2.11.1) (2024-05-17)


### Bug Fixes

* 🐛 token too long in http headers ([6555054](https://github.com/Inist-CNRS/ezs/commit/6555054094edcdd86128df229c0d08f9b06b39bc))





# [2.11.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.8...@ezs/conditor@2.11.0) (2024-03-26)


### Features

* 🎸 use new Corhal POST route ([cc260cf](https://github.com/Inist-CNRS/ezs/commit/cc260cf4ac7e8cd65c1519cb800b323c4fa0ea0b))





## [2.10.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.7...@ezs/conditor@2.10.8) (2024-02-07)

**Note:** Version bump only for package @ezs/conditor





## [2.10.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.6...@ezs/conditor@2.10.7) (2023-09-08)

**Note:** Version bump only for package @ezs/conditor





## [2.10.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.5...@ezs/conditor@2.10.6) (2023-07-17)

**Note:** Version bump only for package @ezs/conditor





## [2.10.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.4...@ezs/conditor@2.10.5) (2023-04-06)


### Bug Fixes

* use POST & envelope for very large tokens ([da93abe](https://github.com/Inist-CNRS/ezs/commit/da93abe21e93f54f1e22b1d2090fc0daa3025a25))





## [2.10.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.3...@ezs/conditor@2.10.4) (2023-03-28)

**Note:** Version bump only for package @ezs/conditor





## [2.10.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.2...@ezs/conditor@2.10.3) (2023-01-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [2.10.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.1...@ezs/conditor@2.10.2) (2023-01-25)

**Note:** Version bump only for package @ezs/conditor





## [2.10.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.10.0...@ezs/conditor@2.10.1) (2023-01-09)

**Note:** Version bump only for package @ezs/conditor





# [2.10.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.9.0...@ezs/conditor@2.10.0) (2023-01-05)


### Features

* 🎸 allow to choose step parameter ([61138b6](https://github.com/Inist-CNRS/ezs/commit/61138b605786f6a5764ae5fed2f1ac1f87f7008e))





# [2.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.8.3...@ezs/conditor@2.9.0) (2022-10-13)


### Bug Fixes

* 🐛 works ([cc60564](https://github.com/Inist-CNRS/ezs/commit/cc60564ea84fca86c6121f10440421e5f828e26d))


### Features

* 🎸 add wos fetch ([6f5dd2c](https://github.com/Inist-CNRS/ezs/commit/6f5dd2cf93cda3f5c7b64223779d6a7b2ff55482))





## [2.8.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.8.2...@ezs/conditor@2.8.3) (2022-09-30)


### Bug Fixes

* 🐛 break the loop correctly ([20dedb3](https://github.com/Inist-CNRS/ezs/commit/20dedb3d116110804f1cd6b44f5df96a145db71c))





## [2.8.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.8.1...@ezs/conditor@2.8.2) (2022-09-07)


### Bug Fixes

* 🐛 stop loop (and refactoring) ([2c162aa](https://github.com/Inist-CNRS/ezs/commit/2c162aa64a4603f79c5bdddbca8b58272278f745))





## [2.8.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.8.0...@ezs/conditor@2.8.1) (2022-09-06)


### Bug Fixes

* 🐛 improve corhal fetch ([178ed93](https://github.com/Inist-CNRS/ezs/commit/178ed93ab3eccf087ad9098c614bbe801fcf8aa1))





# [2.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.7.0...@ezs/conditor@2.8.0) (2022-07-07)


### Features

* **conditor:** Add getRnsrInfo statement ([ee7271f](https://github.com/Inist-CNRS/ezs/commit/ee7271f5d7a80465b54ba276b30f3f0e00e8b435))





# [2.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.6.1...@ezs/conditor@2.7.0) (2022-06-22)


### Features

* 🎸 add [CORHALFetch]x ([a1c8e59](https://github.com/Inist-CNRS/ezs/commit/a1c8e59dc863d36e839fc157e41ebea6a850e17b))





## [2.6.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.6.0...@ezs/conditor@2.6.1) (2022-06-21)

**Note:** Version bump only for package @ezs/conditor





# [2.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.5.0...@ezs/conditor@2.6.0) (2022-06-21)


### Features

* **conditor:** Make 2021 version of RNSR the default one ([03cf179](https://github.com/Inist-CNRS/ezs/commit/03cf179fd69783cfc3cd29a59b2b53c87146dde9))





# [2.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.4.4...@ezs/conditor@2.5.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [2.4.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.4.3...@ezs/conditor@2.4.4) (2022-01-31)

**Note:** Version bump only for package @ezs/conditor





## [2.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.4.2...@ezs/conditor@2.4.3) (2022-01-27)

**Note:** Version bump only for package @ezs/conditor





## [2.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.4.1...@ezs/conditor@2.4.2) (2021-10-05)


### Bug Fixes

* 🐛 fix wrong options + miss dep.x ([5b38d05](https://github.com/Inist-CNRS/ezs/commit/5b38d05199a9a49c73d264f4ddb9a45dd0e64c7e))





## [2.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.4.0...@ezs/conditor@2.4.1) (2021-08-27)

**Note:** Version bump only for package @ezs/conditor





# [2.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.3.2...@ezs/conditor@2.4.0) (2021-07-30)


### Bug Fixes

* **conditor:** Find sigle separated with / ([a04882d](https://github.com/Inist-CNRS/ezs/commit/a04882d668986c487d5990bc9c4d853d19bcde38))
* **conditor:** Fix rnsr.js in node < 14 ([e7b51de](https://github.com/Inist-CNRS/ezs/commit/e7b51def651da99616309471360b65c17b7f61e5))
* **conditor:** split label and numero also on underscore ([8350a5d](https://github.com/Inist-CNRS/ezs/commit/8350a5d17880e98d01220c6099c7b23022e8bc7a))


### Features

* **conditor:** Make getRnsr functional ([6760528](https://github.com/Inist-CNRS/ezs/commit/6760528c75a1eb4c5b7ce8af55c7d1aa79813175))





## [2.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.3.1...@ezs/conditor@2.3.2) (2021-07-22)

**Note:** Version bump only for package @ezs/conditor





## [2.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.3.0...@ezs/conditor@2.3.1) (2021-06-28)

**Note:** Version bump only for package @ezs/conditor





# [2.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.2.2...@ezs/conditor@2.3.0) (2021-06-15)


### Features

* **conditor:** Use .env only in conditorScroll ([47451e5](https://github.com/Inist-CNRS/ezs/commit/47451e59ecf5b6a579534999cade4e13a0ad5140))





## [2.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.2.1...@ezs/conditor@2.2.2) (2021-06-07)

**Note:** Version bump only for package @ezs/conditor





## [2.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.2.0...@ezs/conditor@2.2.1) (2021-06-07)


### Bug Fixes

* **conditor:** Fix followsNumeroLabel ([b64c53c](https://github.com/Inist-CNRS/ezs/commit/b64c53c5333bc88428a3b15d1d4efc5c6cf41cae))
* **conditor:** Fix wrongly assigned RNSR ID ([365ad69](https://github.com/Inist-CNRS/ezs/commit/365ad69d788e276524181bb30d8b19b7a50c3bb8)), closes [#152](https://github.com/Inist-CNRS/ezs/issues/152)





# [2.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.1.2...@ezs/conditor@2.2.0) (2021-06-04)


### Features

* Specified Aggregation query for sub resource ([b594c95](https://github.com/Inist-CNRS/ezs/commit/b594c952b5baa57c818d62f4e9cf6d25d4bd1c7a))





## [2.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.1.1...@ezs/conditor@2.1.2) (2021-05-05)


### Bug Fixes

* 🐛 remove sync code ([0a63cef](https://github.com/Inist-CNRS/ezs/commit/0a63cef73bd0ce854a8e3f0e2d2306f3ecf0b158))





## [2.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.1.0...@ezs/conditor@2.1.1) (2021-04-26)

**Note:** Version bump only for package @ezs/conditor





# [2.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@2.0.0...@ezs/conditor@2.1.0) (2021-04-20)


### Features

* 🎸 add [aggregateQuery] ([06c88f0](https://github.com/Inist-CNRS/ezs/commit/06c88f08d3f67b635482077322fb2c788fd8421a))





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.10.3...@ezs/conditor@2.0.0) (2021-04-20)


### Features

* **conditor:** Replace RNSR param with year ([f90ec24](https://github.com/Inist-CNRS/ezs/commit/f90ec2416aa324044a458bacc330e4a07f76983f))


### BREAKING CHANGES

* **conditor:** Remove old RNSR parameter





## [1.10.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.10.2...@ezs/conditor@1.10.3) (2021-04-09)


### Bug Fixes

* 🐛 unpack crash ([326c6a0](https://github.com/Inist-CNRS/ezs/commit/326c6a0d7470703f339436da2d67cf08a50fa6db))





## [1.10.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.10.1...@ezs/conditor@1.10.2) (2021-04-07)


### Bug Fixes

* 🐛 security patch ([37b826b](https://github.com/Inist-CNRS/ezs/commit/37b826bf8481b5fa92e00c43420037df6edebba6))





## [1.10.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.10.0...@ezs/conditor@1.10.1) (2021-04-02)

**Note:** Version bump only for package @ezs/conditor





# [1.10.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.18...@ezs/conditor@1.10.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





## [1.9.18](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.17...@ezs/conditor@1.9.18) (2020-10-19)

**Note:** Version bump only for package @ezs/conditor





## [1.9.17](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.16...@ezs/conditor@1.9.17) (2020-10-19)

**Note:** Version bump only for package @ezs/conditor





## [1.9.16](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.15...@ezs/conditor@1.9.16) (2020-09-28)


### Bug Fixes

* 🐛 security patch ([06468d5](https://github.com/Inist-CNRS/ezs/commit/06468d56d76c640fb03d7fa73f72d9cc38d44675))





## [1.9.15](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.14...@ezs/conditor@1.9.15) (2020-09-17)

**Note:** Version bump only for package @ezs/conditor





## [1.9.14](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.13...@ezs/conditor@1.9.14) (2020-09-16)

**Note:** Version bump only for package @ezs/conditor





## [1.9.13](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.12...@ezs/conditor@1.9.13) (2020-09-14)

**Note:** Version bump only for package @ezs/conditor





## [1.9.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.11...@ezs/conditor@1.9.12) (2020-09-03)

**Note:** Version bump only for package @ezs/conditor





## [1.9.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.10...@ezs/conditor@1.9.11) (2020-07-27)

**Note:** Version bump only for package @ezs/conditor





## [1.9.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.9...@ezs/conditor@1.9.10) (2020-07-27)

**Note:** Version bump only for package @ezs/conditor





## [1.9.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.8...@ezs/conditor@1.9.9) (2020-06-12)

**Note:** Version bump only for package @ezs/conditor





## [1.9.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.7...@ezs/conditor@1.9.8) (2020-05-11)

**Note:** Version bump only for package @ezs/conditor





## [1.9.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.6...@ezs/conditor@1.9.7) (2020-04-27)

**Note:** Version bump only for package @ezs/conditor





## [1.9.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.5...@ezs/conditor@1.9.6) (2020-02-27)

**Note:** Version bump only for package @ezs/conditor





## [1.9.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.4...@ezs/conditor@1.9.5) (2020-02-26)

**Note:** Version bump only for package @ezs/conditor





## [1.9.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.3...@ezs/conditor@1.9.4) (2020-02-03)

**Note:** Version bump only for package @ezs/conditor





## [1.9.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.1...@ezs/conditor@1.9.3) (2020-01-10)

**Note:** Version bump only for package @ezs/conditor





## [1.9.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.1...@ezs/conditor@1.9.2) (2020-01-10)

**Note:** Version bump only for package @ezs/conditor





## [1.9.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.9.0...@ezs/conditor@1.9.1) (2019-12-20)


### Bug Fixes

* **core:** 🐛 missing package with Travis ([93e2414](https://github.com/Inist-CNRS/ezs/commit/93e24148a7f921852dda1d2ca88a2db05dc55999))





# [1.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.8.1...@ezs/conditor@1.9.0) (2019-12-16)


### Bug Fixes

* **conditor:** Fix conditorScroll ([ee0c253](https://github.com/Inist-CNRS/ezs/commit/ee0c2536a85f21c0eb642f750efc29475f251abc))
* **conditor:** Fix headers use ([96664b3](https://github.com/Inist-CNRS/ezs/commit/96664b3da5f474b22f58f5f64efa325ce6a2c325))
* **conditor:** Fix query in example ([ddab721](https://github.com/Inist-CNRS/ezs/commit/ddab7217c916ff2d3a815b44df32044d7befc7bd))


### Features

* **conditor:** Add conditorScroll ([5b4d9a8](https://github.com/Inist-CNRS/ezs/commit/5b4d9a841b8aae8258e414b353009a6e7b4e1903))
* **conditor:** Add max_page param to conditorScroll ([106c127](https://github.com/Inist-CNRS/ezs/commit/106c127961b302ced208f99df4c22ef06c40d8df))
* **conditor:** Add optional progress bar to conditorScroll ([cc2d7c9](https://github.com/Inist-CNRS/ezs/commit/cc2d7c97a73cbc379cf76095f87f5f93fa1bcdfd))





## [1.8.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.8.0...@ezs/conditor@1.8.1) (2019-12-13)

**Note:** Version bump only for package @ezs/conditor





# [1.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.7.0...@ezs/conditor@1.8.0) (2019-12-03)


### Features

* **conditor:** Take xPublicationDate into account ([1ad2e7c](https://github.com/Inist-CNRS/ezs/commit/1ad2e7cf4847fc0fc9f0cb219f7e7355d79f42b1))





# [1.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.6.0...@ezs/conditor@1.7.0) (2019-11-28)


### Features

* **conditor:** Don't parse numero as an integer ([139683b](https://github.com/Inist-CNRS/ezs/commit/139683bb7ff202f7ad7c0e9a7f51dbbeadab6f59))





# [1.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/conditor@1.5.0...@ezs/conditor@1.6.0) (2019-11-28)


### Bug Fixes

* **conditor:** Fix test ([1a03f36](https://github.com/Inist-CNRS/ezs/commit/1a03f36274bced1f74df9111e61a3fd7281e13d1))
* **conditor:** Make bin work within git repository ([74f633e](https://github.com/Inist-CNRS/ezs/commit/74f633eed470c2807c7869dc0c75261e18793cbd))


### Features

* **conditor:** Make CEDEX test work ([a928f1c](https://github.com/Inist-CNRS/ezs/commit/a928f1c7ff8eb4c333f4512d01309e14b2f97fc3))





# 1.5.0 (2019-11-18)


### Features

* **conditor:** Add compareRnsr statement ([c108bf0](https://github.com/Inist-CNRS/ezs/commit/c108bf005a60879df9a1919f73f9d4a4dee34f72))
* **conditor:** Import ezs-conditor ([d4071a7](https://github.com/Inist-CNRS/ezs/commit/d4071a7d66afc1093662a72e40ccca74b8edf0b3))





# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.4.0](https://github.com/conditor-project/ezs-conditor/compare/v1.3.0...v1.4.0) (2019-11-14)


### Features

* Add findStructuresInAdresses ([bfc86c6](https://github.com/conditor-project/ezs-conditor/commit/bfc86c6a087cf9cf48f2d38093e7c53e66317a7c))
* Add precision in findIdsInAddresses ([1f89d56](https://github.com/conditor-project/ezs-conditor/commit/1f89d567932b4cc76f6a4da34997c11082beb89c))
* Output "wrongly" found structures too ([fa85639](https://github.com/conditor-project/ezs-conditor/commit/fa856396614c75433e4186ca91e8dd16cda52097))


### Bug Fixes

* Fix hasPostalAddress and hasSigle ([1dab775](https://github.com/conditor-project/ezs-conditor/commit/1dab7750515c2db5ceb6b94dda49e4491e50aee3))
* Get label and numero with comma separator ([52f9682](https://github.com/conditor-project/ezs-conditor/commit/52f9682bfd6b6832db1f83590d1f6050d26fef20))
* Use multiple expected ids ([c7841b2](https://github.com/conditor-project/ezs-conditor/commit/c7841b2fdeef67f068bc6c90f8b15c9e43644303))

## [1.3.0](https://github.com/conditor-project/ezs-conditor/compare/v1.2.0...v1.3.0) (2019-11-06)


### Features

* Add prepareRnsrJson ([a93b3ea](https://github.com/conditor-project/ezs-conditor/commit/a93b3eaccb0d565cf9def77706782ef85da2fee8))


### Bug Fixes

* Fix hasTutelle ([de050d2](https://github.com/conditor-project/ezs-conditor/commit/de050d2df6d50906a8c4dc78f9022bbb1ee11c78))

## [1.2.0](https://github.com/conditor-project/ezs-conditor/compare/v1.1.0...v1.2.0) (2019-10-25)


### Features

* Ignore accents ([7c1defb](https://github.com/conditor-project/ezs-conditor/commit/7c1defb3a365064c2265c2173ba3a2b3af73ae8a))

## [1.1.0](https://github.com/conditor-project/ezs-conditor/compare/v1.0.0...v1.1.0) (2019-10-25)


### Features

* Find etabAssoc even in lower case ([eb8dede](https://github.com/conditor-project/ezs-conditor/commit/eb8dedeba5aafa480fec35aee2dc3dd635772152))

## 1.0.0 (2019-10-24)


### Features

* Add affAlign statement ([c635863](https://github.com/conditor-project/ezs-conditor/commit/c635863b26d2d3ad8dc7ed1917fe5b789357f405))
