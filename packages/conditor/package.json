{"name": "@ezs/conditor", "description": "ezs statements for <PERSON><PERSON><PERSON>", "version": "2.13.4", "author": "<PERSON> <<EMAIL>>", "bin": {"affAlign": "./bin/affAlign.js", "compareRnsr": "./bin/compareRnsr.js", "findIdsInAddresses": "./bin/findIdsInAddresses.js", "prepareRnsrJson": "./bin/prepareRnsrJson.js"}, "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"async-each-series": "^1.1.0", "async-retry": "1.3.3", "csv-string": "3.2.0", "debug": "4.3.3", "dotenv": "8.2.0", "fast-xml-parser": "4.4.1", "fetch-with-proxy": "3.0.1", "lodash": "4.17.21", "node-abort-controller": "1.1.0", "progress": "2.0.3", "qs": "6.10.3", "ramda": "0.27.1", "stream-write": "^1.0.1", "unidecode": "0.1.8"}, "gitHead": "bc1be6636627b450c72d59ec404c43d87d7a42aa", "homepage": "https://github.com/Inist-CNRS/ezs/packages/conditor#readme", "keywords": ["affiliations", "alignment", "conditor", "ezs", "stream"], "license": "CECILL-2.1", "main": "lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-conditor.md --section=usage && cp ../../docs/plugin-conditor.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js ./bin/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}