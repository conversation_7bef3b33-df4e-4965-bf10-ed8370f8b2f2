{"name": "@ezs/ark", "description": "ARK dedicated statements for EZS", "version": "1.3.8", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"async": "3.2.2", "inist-ark": "2.1.3", "leveldown": "6.1.0", "levelup": "5.1.1", "lodash": "4.17.21"}, "directories": {"test": "test"}, "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/ark#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-ark.md --section=usage  && cp ../../docs/plugin-ark.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}