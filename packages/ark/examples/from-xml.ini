[use]
plugin = basics
plugin = ark

[XMLParse]
separator = /root/*
separator = /modsCollection/mods
separator = /teiCorpus/TEI
separator = /root/*
separator = /struct/*
separator = /rss/channel/item
separator = /feed/entry
separator = /rows/row
separator = /rows/row
separator = /rdf:RDF/skos:Concept

[arkify]
database = /app/data/.database.ark
naan = env('naan', 67375)
subpublisher = env('subpublisher', 'XXX')
target = env('target', 'uid')


[XMLString]
rootElement = env('rootElement', 'rows')
contentElement = env('contentElement', 'row')

