# Loterre

Ce plugin propose une série d'instructions spécifiques à l’usage de [loterre](https://www.loterre.fr)

## installation

```bash
npm install @ezs/loterre
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [SKOSHierarchy](#skoshierarchy)
*   [SKOSObject](#skosobject)
*   [SKOSPathEnum](#skospathenum)
*   [SKOSToGexf](#skostogexf)

### SKOSHierarchy

Output:

```json
 [
     {
         "source": ...,
         "target": ...,
         "weight": ...
     }
 ]
```

#### Parameters

*   `language` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Choose language of `prefLabel` (optional, default `en`)

Returns **[Promise](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise)** Return fed Object

### SKOSObject

Take `Object` generated by XMLMapping & SKOS data and
create a new basic object with only keys & values

#### Parameters

*   `none` **[undefined](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined)**&#x20;

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### SKOSPathEnum

Take an `Object` and transform "broader","narrower" and "related"
properties to an 'Object' containing the `prefLabel` and `rdf$about`

    <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:skos="http://www.w3.org/2004/02/skos/core#">

         <skos:Concept rdf:about="http://example.com/dishes#potatoBased">
              <skos:prefLabel xml:lang="fr">Plats à base de pomme de terre</skos:prefLabel>
              <skos:prefLabel xml:lang="en">Potato based dishes</skos:prefLabel>
              <skos:prefLabel xml:lang="de">Kartoffelgerichte</skos:prefLabel>
              <skos:inScheme rdf:resource="http://example.com/dishes"/>
              <skos:topConceptOf rdf:resource="http://example.com/dishes"/>
          </skos:Concept>

          <skos:Concept rdf:about="http://example.com/dishes#fries">
              <skos:prefLabel xml:lang="fr">Frites</skos:prefLabel>
              <skos:prefLabel xml:lang="en">French fries</skos:prefLabel>
              <skos:prefLabel xml:lang="de">Französisch frites</skos:prefLabel>
              <skos:inScheme rdf:resource="http://example.com/dishes"/>
              <skos:broader rdf:resource="http://example.com/dishes#potatoBased"/>
          </skos:Concept>

          <skos:Concept rdf:about="http://example.com/dishes#mashed">
              <skos:prefLabel xml:lang="fr">Purée de pomme de terre</skos:prefLabel>
              <skos:prefLabel xml:lang="en">Mashed potatoes</skos:prefLabel>
              <skos:prefLabel xml:lang="de">Kartoffelpüree</skos:prefLabel>
              <skos:inScheme rdf:resource="http://example.com/dishes"/>
              <skos:broader rdf:resource="http://example.com/dishes#potatoBased"/>
          </skos:Concept>

    </rdf:RDF>

Script:

```ini
[use]
plugin = loterre

[concat]
[XMLParse]
separator = /rdf:RDF/skos:Concept
[SKOSObject]

[SKOSPathEnum]
path = broader
path = narrower
label = prefLabel@fr
```

Output:

```json
  [
   {
      "rdf$about": "http://example.com/dishes#fries",
      "prefLabel@fr": "Frites",
      "prefLabel@en": "French fries",
      "prefLabel@de": "Französisch frites",
      "inScheme": "http://example.com/dishes",
      "broader": [ [{ "rdf$about": "http://example.com/dishes#potatoBased", "prefLabel@fr": "Plats à base de pomme de terre" }] ]
    },
    {
      "rdf$about": "http://example.com/dishes#mashed",
      "prefLabel@fr": "Purée de pomme de terre",
      "prefLabel@en": "Mashed potatoes",
      "prefLabel@de": "Kartoffelpüree",
      "inScheme": "http://example.com/dishes",
      "broader": [ [{ "rdf$about": "http://example.com/dishes#potatoBased", "prefLabel@fr": "Plats à base de pomme de terre" }] ]
    },
    {
      "rdf$about": "http://example.com/dishes#potatoBased",
      "prefLabel@fr": "Plats à base de pomme de terre",
      "prefLabel@en": "Potato based dishes",
      "prefLabel@de": "Kartoffelgerichte",
      "inScheme": "http://example.com/dishes",
      "topConceptOf": "http://example.com/dishes",
      "narrower": [
         { "rdf$about": "http://example.com/dishes#fries", "prefLabel@fr": "Frites" },
         {
             "rdf$about": "http://example.com/dishes#mashed",
             "prefLabel@fr": "Purée de pomme de terre"
         }
      ]
    }
  ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Choose one or more paths to enum (optional, default `skos$broader`)
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Choose one path to select uri from found concepts (optional, default `rdf$about`)
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Choose one path to select label from found concepts (optional, default `skos$prefLabel`)
*   `recursion` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Follow path to enum (usefull for broaderConcept) (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** Returns object

### SKOSToGexf

Output:

```json
 [
     {
         "source": ...,
         "target": ...,
         "weight": ...
     }
 ]
```

#### Parameters

*   `language` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Choose language of `prefLabel` (optional, default `en`)

Returns **[Promise](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise)** Return fed Object
