{"name": "@ezs/analytics", "description": "Analytics statements for EZS", "version": "2.3.6", "author": "<PERSON> <<EMAIL>>", "bugs": "https://github.com/Inist-CNRS/ezs/issues", "dependencies": {"@ezs/store": "2.0.6", "async-each-series": "1.1.0", "fast-sort": "2.1.1", "lodash": "4.17.21", "make-dir": "3.1.0", "node-object-hash": "2.3.10", "path-exists": "4.0.0", "tempy": "1.0.1"}, "directories": {"test": "test"}, "gitHead": "bc1be6636627b450c72d59ec404c43d87d7a42aa", "homepage": "https://github.com/Inist-CNRS/ezs/tree/master/packages/analytics#readme", "keywords": ["ezs"], "license": "MIT", "main": "./lib/index.js", "peerDependencies": {"@ezs/core": "*"}, "publishConfig": {"access": "public"}, "repository": "Inist-CNRS/ezs.git", "scripts": {"build": "babel --root-mode upward src --out-dir lib", "doc": "documentation readme src/* --sort-order=alpha --shallow --markdown-toc-max-depth=2 --readme-file=../../docs/plugin-analytics.md --section=usage && cp ../../docs/plugin-analytics.md ./README.md", "lint": "eslint --ext=.js ./test/*.js ./src/*.js", "prepublish": "npm run build", "pretest": "npm run build", "preversion": "npm run doc"}}