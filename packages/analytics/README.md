# analytics

## Présentation

Ce plugin est propose une série d'instructions pour croiser, compter, trier, agréger des flux d’objets Javascript

## installation

```bash
npm install @ezs/analytics
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [aggregate](#aggregate)
*   [count](#count)
*   [distance](#distance)
*   [distinct](#distinct)
*   [distribute](#distribute)
*   [drop](#drop)
*   [exploding](#exploding)
*   [filter](#filter)
*   [graph](#graph)
*   [greater](#greater)
*   [groupingByEquality](#groupingbyequality)
*   [groupingByHamming](#groupingbyhamming)
*   [groupingByLevenshtein](#groupingbylevenshtein)
*   [groupingByModulo](#groupingbymodulo)
*   [keys](#keys)
*   [less](#less)
*   [maximizing](#maximizing)
*   [merging](#merging)
*   [minimizing](#minimizing)
*   [multiply](#multiply)
*   [output](#output)
*   [pair](#pair)
*   [pluck](#pluck)
*   [reducing](#reducing)
*   [segment](#segment)
*   [slice](#slice)
*   [sort](#sort)
*   [statistics](#statistics)
*   [summing](#summing)
*   [tune](#tune)
*   [value](#value)

### aggregate

Aggregate by id and count

```json
[{
         { key: 'x', value: 2 },
         { key: 't', value: 2 },
         { key: 'x', value: 3 },
         { key: 'x', value: 5 },
}]
```

Script:

```ini
[use]
plugin = analytics

[aggregate]
id = key

```

Output:

```json
[
         { id: 'x', value: [ 2, 3, 5] },
         { id: 't', value: [ 2 ]  },
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (if not found 1 is the default value) (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### count

Take `Object` and throw special `Object` like `{id, value}` if key(s) was found
id is the key, value is equal to 1 (if found)

```json
[
 {
      "a": "nancy",
      "b": "lucy",
      "c": "geny",
  },
  {
      "a": "lorem",
      "b": "loret",
  },
  {
      "a": "fred",
  }
]
```

Script:

```ini
[use]
plugin = analytics

[count]
path = a
path = b
path = c

[aggregate]
[summing]

```

Output:

```json
[{
   "id": "a",
   "value": 3
},
{
   "id": "b",
   "value": 2
},
{
   "id": "c",
   "value": 1
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### distance

To compare 2 fields with 2 id and compute a distance

*   for arrays, the distance is calculated according to the number of element in common

```json
[{
           {
              id_of_a: 1,
              id_of_b: 2,
              a: ['x', 'y'],
              b: ['x', 'z'],
          },
          {
              id_of_a: 1,
              id_of_b: 3,
              a: ['x', 'y'],
              b: ['y', 'z'],
          },
          {
              id_of_a: 1,
              id_of_b: 4,
              a: ['x', 'y'],
              b: ['z'],
          },
          {
              id_of_a: 1,
              id_of_b: 5,
              a: ['x', 'y'],
              b: ['x', 'y', 'z'],
          },
          {
              id_of_a: 1,
              id_of_b: 6,
              a: ['x', 'y'],
              b: ['x', 'y'],
          },
}]
```

Script:

```ini
[use]
plugin = analytics

[distance]
id = id_of_a
id = id_of_b
value = a
value = b

```

Output:

```json
[
    { id: [ 1, 2 ], value: 0.5 },
    { id: [ 1, 3 ], value: 0.5 },
    { id: [ 1, 4 ], value: 0 },
    { id: [ 1, 5 ], value: 0.8 },
    { id: [ 1, 6 ], value: 1 }
  ]

]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### distinct

Take `Object` object getting some fields with json path, and do ...

```json
[{
          { a: 'x', b: 'z' },
          { a: 't', b: 'z' },
          { a: 't', b: 'z' },
          { a: 'x', b: 'z' },
          { a: 'x', b: 'z' },
}]
```

Script:

```ini
[use]
plugin = analytics

[distinct]
path = a

```

Output:

```json
[
          { id: 'x', value: 1 },
          { id: 't', value: 1 },
          { id: 't', value: 1 },
          { id: 'x', value: 1 },
          { id: 'x', value: 1 },
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path (optional, default `"id"`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### distribute

Take `Object` like { id, value } and throw a serie of number value

```json
[
          { id: 2000, value: 1 },
          { id: 2001, value: 2 },
          { id: 2003, value: 3 },
          { id: 2005, value: 4 },
          { id: 2007, value: 5 },
          { id: 2009, value: 6 },
          { id: 2011, value: 7 },
          { id: 2013, value: 8 },
]
```

Script:

```ini
[use]
plugin = analytics

[distribute]

```

Output:

```json
[
      { "id": 2000, "value": 1 },
     { "id": 2001, "value": 2 },
     { "id": 2002, "value": 0 },
     { "id": 2003, "value": 3 },
     { "id": 2004, "value": 0 },
     { "id": 2005, "value": 4 },
     { "id": 2006, "value": 0 },
     { "id": 2007, "value": 5 },
     { "id": 2008, "value": 0 },
     { "id": 2009, "value": 6 },
     { "id": 2010, "value": 0 },
     { "id": 2011, "value": 7 },
     { "id": 2012, "value": 0 },
     { "id": 2013, "value": 8 }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `"id"`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `"value"`)
*   `step` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** step between each value (optional, default `1`)
*   `start` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** first value to throw (optional, default `minvalueinthestream`)
*   `size` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** size of the distribution (optional, default `(maxvalue-minvalue)inthestream`)
*   `default` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** default value for missing object (optional, default `0`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### drop

Take `Object` and throw the same object only if there the value of the select field is not equals than a value

```json
[
  {
   "departure": "nancy",
   "arrival": "paris",
 },
 {
   "departure": "nancy",
   "arrival": "toul",
 },
 {
   "departure": "paris",
   "arrival": "londre",
 }
]
```

Script:

```ini
[use]
plugin = analytics

[drop]

```

Output:

```json
[{
  "departure": "nancy",
  "arrival": "paris"
},
{
   "departure": "nancy",
  "arrival": "toul"
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to compare (optional, default `"value"`)
*   `if` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** value to compare (optional, default `""`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### exploding

Take `Object` and take values with \[value] path (must be an array)
and throw object of each value. The new object is build with \[id] and eac value.

```json
[
 { departure: ['tokyo', 'nancy'], arrival: 'toul' },
 { departure: ['paris', 'nancy'], arrival: 'toul' },
 { departure: ['london', 'berlin'], arrival: 'toul' },
}]
```

Script:

```ini
[use]
plugin = analytics

[exploding]

```

Output:

```json
[
   { "id": "toul", "value": "tokyo" },
   { "id": "toul", "value": "nancy" },
   { "id": "toul", "value": "paris" },
   { "id": "toul", "value": "nancy" },
   { "id": "toul", "value": "london" },
   { "id": "toul", "value": "berlin" }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `"id"`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `"value"`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### filter

Take `Object` and throw the same object only if there the value of the select field is equals than a value

*   ```json
    ```

\[
{ id: 2000, value: 1 },
{ id: 2001, value: 2 },
{ id: 2003, value: 3 },
{ id: 2005, value: 4 },
{ id: 2007, value: 5 },
{ id: 2003, value: 3 },
{ id: 2011, value: 7 },
{ id: 2013, value: 8 },
]

````

Script:

```ini
[use]
plugin = analytics

[filter]
path = id
if = 2003
if = 2013

````

Output:

```json
 [
          { id: 2003, value: 3 },
          { id: 2003, value: 3 },
          { id: 2013, value: 8 },
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to compare (optional, default `"value"`)
*   `if` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** value to compare (optional, default `""`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### graph

Take `Object` and throw a new special object (id, value) for each combination of values

```json
[
 { cities: ['berlin', 'nancy', 'toul'] },
 { cities: ['paris', 'nancy', 'toul']},
 { cities: ['paris', 'berlin', 'toul'] },
}]
```

Script:

```ini
[use]
plugin = analytics

[graph]
path = cities

```

Output:

```json
[
  { "id": [ "berlin", "nancy" ], "value": 1 },
  { "id": [ "berlin", "toul" ], "value": 2 },
  { "id": [ "nancy", "toul" ], "value": 2 },
  { "id": [ "nancy", "paris" ], "value": 1 },
  { "id": [ "paris", "toul" ], "value": 2 },
  { "id": [ "berlin", "paris" ], "value": 1 }
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;
*   `identifier` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use to set value result field (if not set or not exists, 1 is use as a default value) (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### greater

Take `Object` and throw the same object only if the value of the selected
field is greater (or equal) than a value

```json
[
          { id: 2000, value: 1 },
          { id: 2001, value: 2 },
          { id: 2003, value: 3 },
          { id: 2005, value: 4 },
          { id: 2007, value: 5 },
          { id: 2009, value: 6 },
          { id: 2011, value: 7 },
          { id: 2013, value: 8 },
]
```

Script:

```ini
[use]
plugin = analytics

[greater]
than = 3
strict = true

```

Output:

```json
[
          { id: 2005, value: 4 },
          { id: 2007, value: 5 },
          { id: 2009, value: 6 },
          { id: 2011, value: 7 },
          { id: 2013, value: 8 },
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to compare (optional, default `"value"`)
*   `than` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** value to compare (optional, default `0`)
*   `strict` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** greater than but not equal (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### groupingByEquality

Take `Object` like `{ id, value }` and reduce all values with the same `id`
in a single object

```json
[
   { "id": "lorem", "value": 1 },
   { "id": "Lorem", "value": 1 },
   { "id": "loren", "value": 1 },
   { "id": "korem", "value": 1 },
   { "id": "olrem", "value": 1 },
   { "id": "toto", "value": 1 },
   { "id": "titi", "value": 1 },
   { "id": "lorem", "value": 1 }
]
```

Script:

```ini
[use]
plugin = analytics

[groupingByEquality]

[summing]

```

Output:

```json
[
  { "id": [ "lorem" ], "value": 2 },
  { "id": [ "Lorem" ], "value": 1 },
  { "id": [ "loren" ], "value": 1 },
  { "id": [ "korem" ], "value": 1 },
  { "id": [ "olrem" ], "value": 1 },
  { "id": [ "toto" ], "value": 1 },
  { "id": [ "titi" ], "value": 1 }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### groupingByHamming

Take `Object` like `{ id, value }` and reduce all `value` with `id` which
have the same Hamming distance in a single object

*   ```json
    ```

\[
{ "id": "lorem", "value": 1 },
{ "id": "Lorem", "value": 1 },
{ "id": "loren", "value": 1 },
{ "id": "korem", "value": 1 },
{ "id": "olrem", "value": 1 },
{ "id": "toto", "value": 1 },
{ "id": "titi", "value": 1 },
{ "id": "lorem", "value": 1 }
]

````

Script:

```ini
[use]
plugin = analytics

[groupingByHamming]
distance = 1

[summing]

````

Output:

```json
[
   { "id": [ "lorem", "Lorem", "loren", "korem" ], "value": 5 },
   { "id": [ "olrem" ], "value": 1 },
   { "id": [ "toto", "titi" ], "value": 2 }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `"id"`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `"value"`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### groupingByLevenshtein

Take `Object` like `{ id, value }` and reduce all `value`s with
`id` which have the same Levenshtein distance in a single object

```json
[
   { "id": "lorem", "value": 1 },
   { "id": "Lorem", "value": 1 },
   { "id": "loren", "value": 1 },
   { "id": "korem", "value": 1 },
   { "id": "olrem", "value": 1 },
   { "id": "toto", "value": 1 },
   { "id": "titi", "value": 1 },
   { "id": "lorem", "value": 1 }
]
```

Script:

```ini
[use]
plugin = analytics

[groupingByLevenshtein]
distance = 2

[summing]

```

Output:

```json
[
   { "id": [ "lorem", "Lorem", "loren", "korem", "olrem" ], "value": 6 },
   { "id": [ "toto", "titi" ], "value": 2 }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `value`)
*   `distance` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** minimal levenshtein distance to have a same id (optional, default `1`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### groupingByModulo

Take `Object` like `{ id, value }` and reduce all `value`s with the same
modulo computation in a ansingle object

```json
[{
}]
```

Script:

```ini
[use]
plugin = analytics

[groupingByModulo]

```

Output:

```json
[
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### keys

Take `Object` and throws all its keys

```json
[
 { city: 'tokyo', year: 2000, count: 1 },
 { city: 'paris', year: 2001, count: 2 },
 { city: 'london', year: 2003, count: 3 },
 { city: 'nancy', year: 2005, count: 4 },
 { city: 'berlin', year: 2007, count: 5 },
 { city: 'madrid', year: 2009, count: 6 },
 { city: 'stockholm', year: 2011, count: 7 },
 { city: 'bruxelles', year: 2013, count: 8 },
]
```

Script:

```ini
[use]
plugin = analytics

[keys]
[aggregate]
[summing]

```

Output:

```json
[
{
   "id": "city",
   "value": 8
},
{
  "id": "year",
  "value": 8
},
{
   "id": "count",
   "value": 8
}⏎
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### less

Take `Object` and throw the same object only if the value of the selected
field is less (or equal) than a value

```json
[{
          { id: 2000, value: 1 },
          { id: 2001, value: 2 },
          { id: 2003, value: 3 },
          { id: 2005, value: 4 },
          { id: 2007, value: 5 },
          { id: 2009, value: 6 },
          { id: 2011, value: 7 },
          { id: 2013, value: 8 },
}]
```

Script:

```ini
[use]
plugin = analytics

[less]
path = value
than = 4

```

Output:

```json
[{
   "id": 2000,
   "value": 1
},
{
   "id": 2001,
  "value": 2
},
{
   "id": 2003,
   "value": 3
},
{
   "id": 2005,
   "value": 4
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to compare (optional, default `value`)
*   `than` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** value to compare (optional, default `0`)
*   `strict` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** less than but not equal (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### maximizing

Take special `Object` like `{id, value}` and replace `value` with the max of `value`s

```json
[
  { id: 'toul', value: [1, 2, 3] },
  { id: 'nancy', value: [2, 3, 4] },
  { id: 'neufchateau', value: [3, 4, 5] },
]
```

Script:

```ini
[use]
plugin = analytics

[maximizing]

```

Output:

```json
[
   { "id": "toul", "value": 3 },
   { "id": "nancy", "value": 4 },
   { "id": "neufchateau", "value": 5 }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### merging

Take special `Object` like `{id, value}` and replace `value` with the merge of `value`s

```json
[{
}]
```

Script:

```ini
[use]
plugin = analytics

[merging]

```

Output:

```json
[
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### minimizing

Take special `Object` like `{id, value}` and replace `value` with the min of
`value`s

```json
[{
}]
```

Script:

```ini
[use]
plugin = analytics

[drop]

```

Output:

```json
[
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for id (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use for value (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### multiply

Take `Object` and throw the same object only if there the value of the select field is equals than a value
Input file:

```json
[{
   a: 1,
   b: 2,
}]
```

Script:

```ini
[use]
plugin = analytics

[multiply]
path = factor
value = X
value = Y
value = Z

```

Output:

```json
[{
   a: 1,
   b: 2,
   factor: X
},
{
   a: 1,
   b: 2,
   factor: Y
},
{
   a: 1,
   b: 2,
   factor: Z
},
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path of the field to add (optional, default `"factor"`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value(s) to set factor field (optional, default `""`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### output

Create an output string containing all incoming elements in a `data` array.
with given `meta` extracted into an object called `meta`.

Créer une sortie en chain de caratere avec les element entrent mise dans un tableau nommé `data`
eyent les donnée `meta` extrais et mises dans un objet appelé `meta`.

##### Script / Scénario

###### ini

```ini
; Import analytics plugin required to use "output"
; Importation du plugin analytique nécessaire pour utiliser "output"
[use]
plugin = analytics

; Using "output" with 'indent' as true and 'meta' as total
; Utilisation de "output" avec 'indent' à vrai et total comme paramètres de 'meta'
[output]
indent = true
meta = total

```

##### Input / Entrée

```json
 [
     { "_id": 1, "value": 2, "total": 2 },
     { "_id": 2, "value": 4, "total": 2 }
 ]
```

##### Output / Sortie

!!! Attention: This is an output function that can only be used at the end of an EZS script. !!!
!!! The output is a string and can't be used with other EZS functions.                       !!!

!!! Attention : Ceci est une fonction de sortie, Elle peut uniquement etre utilisé à la fin d'un script ezs !!!
!!! Cette sortie est une chaine de carater et ne peut pas etre utilisé avec d'autre fonction ezs            !!!

```json
 {
     "data": [
         { "_id": 1, "value": 2 },
         { "_id": 2, "value": 4 }
     ],
     "meta": {
         "total": 2
     }
 }
```

#### Parameters

*   `indent` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** <ul><li>indent the output json</li></ul>
    <ul><li>indenté le json de sortie</li></ul> (optional, default `false`)
*   `meta` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** <ul><li>element from the input to put it in the `meta` object</li></ul>
    <ul><li>élément a extraire de l'entrée et a mettre dans l'objet `meta`</li></ul>

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### pair

Create a pair with 'id' containing a pair of the given 'path's and 'value' set to 1.

Créer un couple 'id' contenent un couple des 'path's donnée et 'value' mise à 1.

##### Script / Scénario

```ini
; Import analytics plugin required to use "pair"
; Importation du plugin analytique nécessaire pour utiliser "pair"
[use]
plugin = analytics

; Using "pair" with 'departure' and 'arrival' as paths setttings
; Utilisation de "pair" avec 'departure' et 'arrival' comme paramètres de paths
[pair]
path = departure
path = arrival

```

##### Input / Entrée

```json
 [
     { "departure": ["tokyo", "nancy"], "arrival": "toul" },
     { "departure": ["paris", "nancy"], "arrival": "toul" },
     { "departure": ["london", "berlin"], "arrival": "toul" }
 ]
```

##### Output / Sortie

```json
 [
     { "id": ["tokyo", "toul"], "value": 1 },
     { "id": ["nancy", "toul"], "value": 1 },
     { "id": ["paris", "toul"], "value": 1 },
     { "id": ["nancy", "toul"], "value": 1 },
     { "id": ["london", "toul"], "value": 1 },
     { "id": ["berlin", "toul"], "value": 1 }
 ]
```

#### Parameters

*   `identifier` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path to use to set value result field (if not set or not exists, 1 is use as a default value) (optional, default `false`)
*   `null` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element who will be use to create the pair</li></ul>
    <ul><li>chemin de l'élément qui vas etre utilisé pour créer le couple</li></ul>

Returns **{id: [Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>, value: `1`}**&#x20;

### pluck

Extract the value of a given `path` and create a pair with the `path` as the `id`
and `path` value as the `value`.

Extrais la valeur d'un `path` donnée et créer un couple avec pour identifient le `path`
et comme `value` la valeur du `path`.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "pluck"
; Importation du plugin analytique nécessaire pour utiliser "pluck"
[use]
plugin = analytics

; Using "pluck" with 'year' as path setttings instead of 'id' how is the default value
; Utilisation de "pluck" avec 'year' comme paramètres de path au lieux de la valeur par defaut qui et 'id'
[pluck]
path = year

```

##### Input / Entrée

```json
 [
     { "city": "tokyo", "year": 2000, "count": 1 },
     { "city": "paris", "year": 2001, "count": 2 },
     { "city": "london", "year": 2003, "count": 3 },
     { "city": "nancy", "year": 2005, "count": 4 },
     { "city": "berlin", "year": 2007, "count": 5 },
     { "city": "madrid", "year": 2009, "count": 6 },
     { "city": "stockholm", "year": 2011, "count": 7 },
     { "city": "bruxelles", "year": 2013, "count": 8 }
 ]
```

##### Output / Sortie

```json
 [
     { "id": "year", "value": 2000 },
     { "id": "year", "value": 2001 },
     { "id": "year", "value": 2003 },
     { "id": "year", "value": 2005 },
     { "id": "year", "value": 2007 },
     { "id": "year", "value": 2009 },
     { "id": "year", "value": 2011 },
     { "id": "year", "value": 2013 }
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element who need to be extrated</li></ul>
    <ul><li>chemin de l'élément qui doit être extrais</li></ul> (optional, default `id`)

Returns **{id: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String), value: [Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)}**&#x20;

### reducing

Merges the `id`, `value` pairs into a new pair, associating the identifier with the values.

Fusionne les couple `id`, `value`, en un nouveau couple associent l'identifient au valeurs.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "reducing"
; Importation du plugin analytique nécessaire pour utiliser "reducing"
[use]
plugin = analytics

; Using "reducing" with default settings
; Utilisation de "reducing" avec les paramètres par défaut
[reducing]
; id = id
; value = value

```

##### Input / Entrée

```json
 [
     { "id": "x", "value": 2 },
     { "id": "t", "value": 2 },
     { "id": "x", "value": 3 },
     { "id": "x", "value": 5 }
 ]
```

##### Output / Sortie

```json
 [
     { "id": "x", "value": [2, 3, 5] },
     { "id": "t", "value": [2] }
 ]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element who will be use as the key</li></ul>
    <ul><li>chemin de l'élément qui vas être utilisé comme clé</li></ul> (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element who will be merge into an array</li></ul>
    <ul><li>chemin de l'élément qui vas être fussioné en un tableau</li></ul> (optional, default `value`)

Returns **{id: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String), value: [Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>}**&#x20;

### segment

Returns an object containing a segmentation of the input.

*   `[a,b,c]` will be returned as `[a,b], [b,c]`

Renvoie un objet contenant une segmentation de l'entrée.

*   `[a,b,c]` sera retourné sous la forme `[a,b], [b,c]`

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "segment"
; Importation du plugin analytique nécessaire pour utiliser "segment"
[use]
plugin = analytics

; Using "segment" with default settings
; Utilisation de "segment" avec les paramètres par défaut
[segment]
; aggregate = true
; identifier = false
; path = value

```

##### Input / Entrée

```json
 [
     {
         "id": "doc#1",
         "value": [
             1,
             2,
             3,
             4
         ]
     },
     {
         "id": "doc#2",
         "value": [
             4,
             5,
             6
         ]
     },
     {
         "id": "doc#3",
         "value": [
             6,
             7
         ]
     },
     {
         "id": "doc#4",
         "value": [
             1,
             2,
             3,
             4,
             5,
             6,
             7
         ]
     }
 ]
```

##### Output / Sortie

```json
 [
     { "id": [ 1, 2 ], "value": 1 },
     { "id": [ 2, 3 ], "value": 1 },
     { "id": [ 3, 4 ], "value": 1 },
     { "id": [ 4, 5 ], "value": 1 },
     { "id": [ 5, 6 ], "value": 1 },
     { "id": [ 6, 7 ], "value": 1 },
     { "id": [ 1, 2 ], "value": 1 },
     { "id": [ 2, 3 ], "value": 1 },
     { "id": [ 3, 4 ], "value": 1 },
     { "id": [ 4, 5 ], "value": 1 },
     { "id": [ 5, 6 ], "value": 1 },
     { "id": [ 6, 7 ], "value": 1 }
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element who need to be segmented</li></ul>
    <ul><li>chemin de l'élément qui doit être segmentés</li></ul> (optional, default `value`)
*   `aggregate` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** aggregate all values for all paths (or not)<ul><li>aggregate all segmented value in one element (work if you have multiple path)</li></ul>
    <ul><li>agréger toutes les valeurs segmentées en un seul élément (fonctionne si vous avez plusieurs chemins)</li></ul> (optional, default `true`)
*   `identifier` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element who will be put in value field (if not set, fallback to `1`)</li></ul>
    <ul><li>chemin de l'élément qui sera mis dans le champ valeur (si non défini, fallback à `1`)</li></ul> (optional, default `false`)

Returns **{id: [Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)>, value: [Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)}**&#x20;

### slice

Returns a copy of a section of a stream.

Renvoie une copie d'une section d'un flux.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "slice"
; Importation du plugin analytique nécessaire pour utiliser "slice"
[use]
plugin = analytics

; Using "slice" with default settings
; Utilisation de "slice" avec les paramètres par défaut
[slice]
; start = 1
; size = 10

```

##### Input / Entrée

```json
 [
     { "id": 2023, "value": 12 },
     { "id": 2021, "value": 11 },
     { "id": 2019, "value": 10 },
     { "id": 2017, "value": 9 },
     { "id": 2013, "value": 8 },
     { "id": 2011, "value": 7 },
     { "id": 2009, "value": 6 },
     { "id": 2007, "value": 5 },
     { "id": 2005, "value": 4 },
     { "id": 2003, "value": 3 },
     { "id": 2001, "value": 2 },
     { "id": 2000, "value": 1 }
 ]
```

##### Output / Sortie

```json
 [
     { "id": 2023, "value": 12 },
     { "id": 2021, "value": 11 },
     { "id": 2019, "value": 10 },
     { "id": 2017, "value": 9 },
     { "id": 2013, "value": 8 },
     { "id": 2011, "value": 7 },
     { "id": 2009, "value": 6 },
     { "id": 2007, "value": 5 },
     { "id": 2005, "value": 4 },
     { "id": 2003, "value": 3 }
 ]
```

#### Parameters

*   `start` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** <ul><li>the beginning index of the specified portion of the stream</li></ul>
    <ul><li>l'indice de début de la partie spécifiée du flux</li></ul> (optional, default `1`)
*   `size` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** <ul><li>the size of the specified portion of the stream</li></ul>
    <ul><li>la taille de début de la partie spécifiée du flux</li></ul> (optional, default `10`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### sort

Sort incomming objects based on the value of the given path.

Trie les objets entrants en fonction de la valeur du champ donné par `path`.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "sort"
; Importation du plugin analytique nécessaire pour utiliser "sort"
[use]
plugin = analytics

; Using "sort" with default settings
; Utilisation de "sort" avec les paramètres par défaut
[sort]
; path = id
; reverse = false

```

##### Input / Entrée

```json
 [
     { "id": 2013, "value": 8 },
     { "id": 2011, "value": 7 },
     { "id": 2009, "value": 6 },
     { "id": 2007, "value": 5 },
     { "id": 2005, "value": 4 },
     { "id": 2003, "value": 3 },
     { "id": 2001, "value": 2 },
     { "id": 2000, "value": 1 }
 ]
```

##### Output / Sortie

```json
 [
     { "id": 2000, "value": 1 },
     { "id": 2001, "value": 2 },
     { "id": 2003, "value": 3 },
     { "id": 2005, "value": 4 },
     { "id": 2007, "value": 5 },
     { "id": 2009, "value": 6 },
     { "id": 2011, "value": 7 },
     { "id": 2013, "value": 8 }
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element used to as reference for the sort</li></ul>
    <ul><li>chemin de l'élément utilisé comme référence pour le tri</li></ul> (optional, default `id`)
*   `reverse` **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** <ul><li>sort in ascending or descending order</li></ul>
    <ul><li>trier par ordre croissant ou décroissant</li></ul> (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### statistics

Analyse and create statistics from given fields.

Analyse et crée des statistiques à partir des champs donnés.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "statistics"
; Importation du plugin analytique nécessaire pour utiliser "statistics"
[use]
plugin = analytics

; Using "statistics" with default settings
; Utilisation de "statistics" avec les paramètres par défaut
[statistics]
; path = value
; target = _statistics

```

##### Input / Entrée

```json
 [
     { "value": 1 },
     { "value": 1 },
     { "value": 2 },
     { "value": 3 },
     { "value": 3 },
     { "value": 3 }
 ]
```

##### Output / Sortie

```json
 [
     {
         "value": 1,
         "_statistics": {
             "value": {
                 "sample": 2,
                 "frequency": 1,
                 "percentage": 25,
                 "sum": 4,
                 "count": 3,
                 "min": 1,
                 "max": 2,
                 "mean": 1.3333333333333333,
                 "range": 1,
                 "midrange": 0.5,
                 "variance": 0.2222222222222222,
                 "deviation": 0.4714045207910317,
                 "population": 2
             }
         }
     },
     {
         "value": 1,
         "_statistics": {
             "value": {
                 "sample": 2,
                 "frequency": 1,
                 "percentage": 25,
                 "sum": 4,
                 "count": 3,
                 "min": 1,
                 "max": 2,
                 "mean": 1.3333333333333333,
                 "range": 1,
                 "midrange": 0.5,
                 "variance": 0.2222222222222222,
                 "deviation": 0.4714045207910317,
                 "population": 2
             }
         }
     },
     {
         "value": 2,
         "_statistics": {
             "value": {
                 "sample": 1,
                 "frequency": 0.5,
                 "percentage": 50,
                 "sum": 4,
                 "count": 3,
                 "min": 1,
                 "max": 2,
                 "mean": 1.3333333333333333,
                 "range": 1,
                 "midrange": 0.5,
                 "variance": 0.2222222222222222,
                 "deviation": 0.4714045207910317,
                 "population": 2
             }
         }
     }
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element used to create the statistics</li></ul>
    <ul><li>chemin de l'élément utilisé pour créer les statistiques</li></ul> (optional, default `value`)
*   `target` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the statistics in the returned object</li></ul>
    <ul><li>chemin des stastistiques dans l'objet retourné</li></ul> (optional, default `_statistics`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### summing

Create an `id`, `value` pair from two given paths and apply a sum to the value.

Créer un couple `id`, `value` à partir de chemins et applique une somme sur `value`.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "summing"
; Importation du plugin analytique nécessaire pour utiliser "summing"
[use]
plugin = analytics

; Using "summing" with default settings
; Utilisation de "summing" avec les paramètres par défaut
[summing]
; id = id
; value = value

```

##### Input / Entrée

```json
 [
     {
         "id": 1,
         "value": [1, 1, 1],
         "hello": "world"
     },
     {
         "id": 2,
         "value": [2, 2, 2],
         "hello": "world"
     }
 ]
```

##### Output / Sortie

```json
[
    {
        "id": 1,
        "value": 3
    },
    {
        "id": 2,
        "value": 6
    }
]
```

#### Parameters

*   `id` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element used to create the new identifier</li></ul>
    <ul><li>chemin de l'élément utilisé pour créer le nouvel identifiant</li></ul> (optional, default `id`)
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element to be summed</li></ul>
    <ul><li>chemin de l'élément qui doit être sommé</li></ul> (optional, default `value`)

Returns **{id: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String), value: [Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)}**&#x20;

### tune

Create and replace the id with a unified id that can be used with [sort](#sort).

Créer et remplacer l'identifiant par un identifiant unifié qui peut être utilisé avec [sort](#sort).

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "tune"
; Importation du plugin analytique nécessaire pour utiliser "tune"
[use]
plugin = analytics

; Using "tune" with default settings
; Utilisation de "tune" avec les paramètres par défaut
[tune]
; path = id
; method = natural

```

##### Input / Entrée

```json
 [
     {
         "id": 1,
         "value": 1
     },
     {
         "id": 2,
         "value": 2
     }
 ]
```

##### Output / Sortie

```json
 [
     {
         "id": "0000000000000000001.00000000000000000000",
         "value": {
             "id": 1,
             "value": 1,
             "label": "static value"
         }
     },
     {
         "id": "0000000000000000002.00000000000000000000",
         "value": {
             "id": 2,
             "value": 2,
             "label": "static value"
         }
     }
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element used to create the unified identifier</li></ul>
    <ul><li>chemin de l'élément utilisé pour créer l'identifiant unifié</li></ul> (optional, default `id`)
*   `method` **(`"natural"` | `"levenshtein"` | `"numerical"`)** <ul><li>method used to create the unified identifier</li></ul>
     <ul><ul><li>natural - Create a normalised identifier that is set to a fixed length</li></ul></ul>
     <ul><ul><li>levenshtein - Create an identifier based on the Levenshtein algorithm</li></ul></ul>
     <ul><ul><li>numerical - Create an identifier based on a numeric value</li></ul></ul><ul><li>méthode utilisée pour créer l'identifiant unifié</li></ul>
     <ul><ul><li>natural - Crée un identifiant normalisé de longueur fixe</li></ul></ul>
     <ul><ul><li>levenshtein - Crée un identifiant basé sur l'algorithme de Levenshtein</li></ul></ul>
     <ul><ul><li>numerical - Crée un identifiant basé sur une valeur numérique</li></ul></ul> (optional, default `natural`)

Returns **{id: [String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String), value: [Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)}**&#x20;

### value

Create a new object from the value of the given `path`.

Créer un nouvel objet à partir du chemin donné dans `path`.

#### Example / Exemple

##### Script / Scénario

```ini
; Import analytics plugin required to use "value"
; Importation du plugin analytique nécessaire pour utiliser "value"
[use]
plugin = analytics

; Using "value" with default settings
; Utilisation de "tune" avec les paramètres par défaut
[value]
; path = value

```

##### Input / Entrée

###### Dataset 1 / Jeu de données 1

```json
 [
     { "id": 2000, "value": 1 },
     { "id": 2001, "value": 2 },
     { "id": 2003, "value": 3 },
     { "id": 2005, "value": 4 },
     { "id": 2007, "value": 5 },
     { "id": 2009, "value": 6 },
     { "id": 2011, "value": 7 },
     { "id": 2013, "value": 8 }
 ]
```

###### Dataset 2 / Jeu de données 2

```json
 [
     {
         "id": 1,
         "value": {
             "hello": "world"
         }
     },
     {
         "id": 2,
         "value": {
             "hello": "ezs"
         }
     },
     {
         "id": 3,
         "value": {
             "hello": "lodex"
         }
     }
 ]
```

##### Output / Sortie

###### Dataset 1 / Jeu de données 1

```json
 [
     1,
     2,
     3,
     4,
     5,
     6,
     7,
     8
 ]
```

###### Dataset 2 / Jeu de données 2

```json
 [
     {
         "hello": "world"
     },
     {
         "hello": "ezs"
     },
     {
         "hello": "lodex"
     }
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** <ul><li>path of the element used to create the new object</li></ul>
    <ul><li>chemin de l'élément utilisé pour créer le nouvel objet</li></ul> (optional, default `value`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;
