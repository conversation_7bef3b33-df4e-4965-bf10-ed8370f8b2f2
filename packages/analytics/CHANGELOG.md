# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.3.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.3.5...@ezs/analytics@2.3.6) (2025-09-12)

**Note:** Version bump only for package @ezs/analytics





## [2.3.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.3.4...@ezs/analytics@2.3.5) (2025-01-13)

**Note:** Version bump only for package @ezs/analytics





## [2.3.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.3.3...@ezs/analytics@2.3.4) (2024-11-22)

**Note:** Version bump only for package @ezs/analytics





## [2.3.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.3.2...@ezs/analytics@2.3.3) (2024-11-05)

**Note:** Version bump only for package @ezs/analytics





## [2.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.3.1...@ezs/analytics@2.3.2) (2024-03-25)

**Note:** Version bump only for package @ezs/analytics





## [2.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.3.0...@ezs/analytics@2.3.1) (2024-02-09)

**Note:** Version bump only for package @ezs/analytics





# [2.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.2.2...@ezs/analytics@2.3.0) (2024-02-07)


### Bug Fixes

* update lodash import ([b51c4d5](https://github.com/Inist-CNRS/ezs/commit/b51c4d50a23e80c838e86ac2c911e678c764b990))


### Features

* 🎸 add identifier param (like [graph]) ([855916a](https://github.com/Inist-CNRS/ezs/commit/855916aff4a8ae6fc3b68e6c06435b4939ced812))





## [2.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.2.1...@ezs/analytics@2.2.2) (2024-01-26)

**Note:** Version bump only for package @ezs/analytics





## [2.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.2.0...@ezs/analytics@2.2.1) (2023-09-08)

**Note:** Version bump only for package @ezs/analytics





# [2.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.1.0...@ezs/analytics@2.2.0) (2023-09-08)


### Features

* 🎸 add identifier parameter to [graph] ([961e201](https://github.com/Inist-CNRS/ezs/commit/961e20157816b3792ff6ee692e8fd5a6b437968c))





# [2.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.23...@ezs/analytics@2.1.0) (2023-08-25)


### Features

* 🎸 add identifier parametee to [segement] ([bf66392](https://github.com/Inist-CNRS/ezs/commit/bf663926db6f664368b5a36e391fd25ad2d67ed0))





## [2.0.23](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.22...@ezs/analytics@2.0.23) (2023-07-17)

**Note:** Version bump only for package @ezs/analytics





## [2.0.22](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.21...@ezs/analytics@2.0.22) (2023-03-28)

**Note:** Version bump only for package @ezs/analytics





## [2.0.21](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.20...@ezs/analytics@2.0.21) (2023-03-07)

**Note:** Version bump only for package @ezs/analytics





## [2.0.20](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.19...@ezs/analytics@2.0.20) (2023-03-07)

**Note:** Version bump only for package @ezs/analytics





## [2.0.19](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.18...@ezs/analytics@2.0.19) (2023-02-27)

**Note:** Version bump only for package @ezs/analytics





## [2.0.18](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.17...@ezs/analytics@2.0.18) (2023-02-17)

**Note:** Version bump only for package @ezs/analytics





## [2.0.17](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.16...@ezs/analytics@2.0.17) (2023-02-15)

**Note:** Version bump only for package @ezs/analytics





## [2.0.16](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.15...@ezs/analytics@2.0.16) (2023-02-08)

**Note:** Version bump only for package @ezs/analytics





## [2.0.15](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.14...@ezs/analytics@2.0.15) (2023-01-28)


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





## [2.0.14](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.13...@ezs/analytics@2.0.14) (2023-01-25)

**Note:** Version bump only for package @ezs/analytics





## [2.0.13](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.12...@ezs/analytics@2.0.13) (2023-01-20)

**Note:** Version bump only for package @ezs/analytics





## [2.0.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.11...@ezs/analytics@2.0.12) (2023-01-13)

**Note:** Version bump only for package @ezs/analytics





## [2.0.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.10...@ezs/analytics@2.0.11) (2023-01-05)

**Note:** Version bump only for package @ezs/analytics





## [2.0.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.9...@ezs/analytics@2.0.10) (2022-12-22)

**Note:** Version bump only for package @ezs/analytics





## [2.0.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.8...@ezs/analytics@2.0.9) (2022-12-22)

**Note:** Version bump only for package @ezs/analytics





## [2.0.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.7...@ezs/analytics@2.0.8) (2022-12-22)

**Note:** Version bump only for package @ezs/analytics





## [2.0.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.6...@ezs/analytics@2.0.7) (2022-12-22)

**Note:** Version bump only for package @ezs/analytics





## [2.0.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.5...@ezs/analytics@2.0.6) (2022-12-21)

**Note:** Version bump only for package @ezs/analytics





## [2.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.4...@ezs/analytics@2.0.5) (2022-12-14)

**Note:** Version bump only for package @ezs/analytics





## [2.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.3...@ezs/analytics@2.0.4) (2022-12-02)

**Note:** Version bump only for package @ezs/analytics





## [2.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.2...@ezs/analytics@2.0.3) (2022-11-28)

**Note:** Version bump only for package @ezs/analytics





## [2.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.1...@ezs/analytics@2.0.2) (2022-09-21)

**Note:** Version bump only for package @ezs/analytics





## [2.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@2.0.0...@ezs/analytics@2.0.1) (2022-09-19)

**Note:** Version bump only for package @ezs/analytics





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.19.5...@ezs/analytics@2.0.0) (2022-09-16)


### Bug Fixes

* 🐛 empty sub pipeline ([68bef49](https://github.com/Inist-CNRS/ezs/commit/68bef49f66ff2b4558df44803aafabcba7b108c5))
* new name ([e7c3da5](https://github.com/Inist-CNRS/ezs/commit/e7c3da541b06fa643330a8ea562f4d140a8e532f))
* new name ([f9a3db5](https://github.com/Inist-CNRS/ezs/commit/f9a3db5e00969ffb65479181169b7c84824b3934))


### Code Refactoring

* 💡 store.close() ([34fa4c3](https://github.com/Inist-CNRS/ezs/commit/34fa4c3b9af943c7523f5dc8926567e2401ee669))


### Features

* 🎸 move [expand] & [combine] to core ([1b97856](https://github.com/Inist-CNRS/ezs/commit/1b97856bbff4008388253dad273653d52af1c87f))


### BREAKING CHANGES

* 🧨 drop [files] [uplaod] [buffers] [bufferize]





## [1.19.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.19.4...@ezs/analytics@1.19.5) (2022-09-15)


### Bug Fixes

* avoid to lose items ([c45d206](https://github.com/Inist-CNRS/ezs/commit/c45d2066d25f66cd72d907a50b6713ffd1ad7dca))





## [1.19.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.19.3...@ezs/analytics@1.19.4) (2022-09-14)


### Bug Fixes

* 🐛 feed.flow return a Promise ([e73f140](https://github.com/Inist-CNRS/ezs/commit/e73f14042eb60b464c9e021345e479d24ba0ec81))





## [1.19.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.19.2...@ezs/analytics@1.19.3) (2022-09-08)

**Note:** Version bump only for package @ezs/analytics





## [1.19.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.19.1...@ezs/analytics@1.19.2) (2022-09-06)

**Note:** Version bump only for package @ezs/analytics





## [1.19.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.19.0...@ezs/analytics@1.19.1) (2022-09-04)


### Bug Fixes

* 🐛 enable memoize ([b571fca](https://github.com/Inist-CNRS/ezs/commit/b571fca2c2ab3fa309d29372256359545e2a0763))





# [1.19.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.8...@ezs/analytics@1.19.0) (2022-09-04)


### Features

* use memory ([8caa855](https://github.com/Inist-CNRS/ezs/commit/8caa8553ef88fc00d7c52732a7f67cf862a87281))





## [1.18.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.7...@ezs/analytics@1.18.8) (2022-06-30)

**Note:** Version bump only for package @ezs/analytics





## [1.18.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.6...@ezs/analytics@1.18.7) (2022-06-21)


### Bug Fixes

* 🐛 increase timeout ([3fc5fcd](https://github.com/Inist-CNRS/ezs/commit/3fc5fcdf347ae8d167694e5185679e28cc95f854))





## [1.18.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.5...@ezs/analytics@1.18.6) (2022-05-25)


### Bug Fixes

* better test ([2a05c34](https://github.com/Inist-CNRS/ezs/commit/2a05c347ffdff98c7740c611eb62562cce2eafbe))





## [1.18.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.4...@ezs/analytics@1.18.5) (2022-05-24)

**Note:** Version bump only for package @ezs/analytics





## [1.18.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.3...@ezs/analytics@1.18.4) (2022-04-02)


### Bug Fixes

* 🐛 erratic error ([cf15306](https://github.com/Inist-CNRS/ezs/commit/cf15306531070939295ff8fa922795e1fd7a2fb2))





## [1.18.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.2...@ezs/analytics@1.18.3) (2022-04-01)


### Bug Fixes

* erratic error with store ([a26febc](https://github.com/Inist-CNRS/ezs/commit/a26febc4fe7bc0a66a7d32781dc6ef175f707f0a))





## [1.18.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.1...@ezs/analytics@1.18.2) (2022-03-30)


### Bug Fixes

* 🐛 [combine] cache works like [expand] ([bb5db12](https://github.com/Inist-CNRS/ezs/commit/bb5db12bfd35612f7a4beb2b2206e8a87d77b62e))





## [1.18.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.18.0...@ezs/analytics@1.18.1) (2022-03-25)

**Note:** Version bump only for package @ezs/analytics





# [1.18.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.17.0...@ezs/analytics@1.18.0) (2022-03-20)


### Features

* 🎸 add [throttle] ([29a4040](https://github.com/Inist-CNRS/ezs/commit/29a404042c7db01cdc68a99b0cfb820d055f7814))





# [1.17.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.18...@ezs/analytics@1.17.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.16.18](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.17...@ezs/analytics@1.16.18) (2022-02-04)

**Note:** Version bump only for package @ezs/analytics





## [1.16.17](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.16...@ezs/analytics@1.16.17) (2022-01-31)

**Note:** Version bump only for package @ezs/analytics





## [1.16.16](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.15...@ezs/analytics@1.16.16) (2022-01-31)


### Bug Fixes

* 🐛 add warning for back pressure control ([beb67f8](https://github.com/Inist-CNRS/ezs/commit/beb67f837ac4589221d0a2bf4192f7e3ca9b25d3))





## [1.16.15](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.14...@ezs/analytics@1.16.15) (2022-01-27)


### Bug Fixes

* 🐛 improve NUMBER transtype operation ([b33dd88](https://github.com/Inist-CNRS/ezs/commit/b33dd887ea96e36f63ee55d12f18c22894223e5b))





## [1.16.14](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.13...@ezs/analytics@1.16.14) (2022-01-06)


### Bug Fixes

* 🐛 the right error for wrong location ([1337817](https://github.com/Inist-CNRS/ezs/commit/13378171ac457d006a5aecafcd4681cd550192d8))





## [1.16.13](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.12...@ezs/analytics@1.16.13) (2021-12-15)

**Note:** Version bump only for package @ezs/analytics





## [1.16.12](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.11...@ezs/analytics@1.16.12) (2021-12-13)

**Note:** Version bump only for package @ezs/analytics





## [1.16.11](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.10...@ezs/analytics@1.16.11) (2021-12-06)

**Note:** Version bump only for package @ezs/analytics





## [1.16.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.9...@ezs/analytics@1.16.10) (2021-11-25)


### Bug Fixes

* 🐛 do not close persistent store ([79641b1](https://github.com/Inist-CNRS/ezs/commit/79641b106f48aa6933aff98679213ac0270acf84))





## [1.16.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.8...@ezs/analytics@1.16.9) (2021-10-30)

**Note:** Version bump only for package @ezs/analytics





## [1.16.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.7...@ezs/analytics@1.16.8) (2021-10-29)


### Bug Fixes

* 🐛 close store everywhere ([6688b33](https://github.com/Inist-CNRS/ezs/commit/6688b333473e90a3586df7f441e91da36847bf12))
* 🐛 tests ([bde57e7](https://github.com/Inist-CNRS/ezs/commit/bde57e7f3f5f17263a81beefde19f9c944d0ff11))





## [1.16.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.6...@ezs/analytics@1.16.7) (2021-10-29)


### Bug Fixes

* 🐛 tests ([d376d0c](https://github.com/Inist-CNRS/ezs/commit/d376d0c631d64a66999f2bf5a4b87af4f2192a0f))





## [1.16.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.5...@ezs/analytics@1.16.6) (2021-10-05)

**Note:** Version bump only for package @ezs/analytics





## [1.16.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.4...@ezs/analytics@1.16.5) (2021-09-27)


### Bug Fixes

* **analytics:** Add lacking dependency node-object-hash ([fd1f3d9](https://github.com/Inist-CNRS/ezs/commit/fd1f3d9da1affbe71d19ee1d3c07fca61459e184))





## [1.16.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.3...@ezs/analytics@1.16.4) (2021-07-22)


### Bug Fixes

* 🐛 to avoid losing items that were skipped ([bca9cba](https://github.com/Inist-CNRS/ezs/commit/bca9cbaf5a63a7d16bfa28a8df287816e765ea92))





## [1.16.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.2...@ezs/analytics@1.16.3) (2021-07-15)

**Note:** Version bump only for package @ezs/analytics





## [1.16.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.1...@ezs/analytics@1.16.2) (2021-07-15)

**Note:** Version bump only for package @ezs/analytics





## [1.16.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.16.0...@ezs/analytics@1.16.1) (2021-06-28)

**Note:** Version bump only for package @ezs/analytics





# [1.16.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.15.1...@ezs/analytics@1.16.0) (2021-06-16)


### Features

* 🎸 fix exampn for empty arrayx ([8af4b92](https://github.com/Inist-CNRS/ezs/commit/8af4b9283df2aaa0717297275b5d6596bf0a18ac))





## [1.15.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.15.0...@ezs/analytics@1.15.1) (2021-06-04)

**Note:** Version bump only for package @ezs/analytics





# [1.15.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.14.2...@ezs/analytics@1.15.0) (2021-05-27)


### Features

* 🎸 add [XMLConvert] ([0040fce](https://github.com/Inist-CNRS/ezs/commit/0040fce60affd263f537853e5fd4957df43d6c7a))





## [1.14.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.14.1...@ezs/analytics@1.14.2) (2021-05-05)


### Bug Fixes

* 🐛 remove sync code ([0a63cef](https://github.com/Inist-CNRS/ezs/commit/0a63cef73bd0ce854a8e3f0e2d2306f3ecf0b158))





## [1.14.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.14.0...@ezs/analytics@1.14.1) (2021-05-05)


### Bug Fixes

* 🐛 enable some options ([bbadf08](https://github.com/Inist-CNRS/ezs/commit/bbadf08dd293f1b91dc6098d959e11289f873dc3))





# [1.14.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.13.0...@ezs/analytics@1.14.0) (2021-05-04)


### Bug Fixes

* 🐛 fix tests ([94dad99](https://github.com/Inist-CNRS/ezs/commit/94dad99565e03d527059cc9b84efd58e4d99fb0c))


### Features

* 🎸 add [upload] ([36493b1](https://github.com/Inist-CNRS/ezs/commit/36493b13d728999621a5a1a1d590ea01a12fbffd))





# [1.13.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.12.1...@ezs/analytics@1.13.0) (2021-04-07)


### Features

* 🎸 add option default ([dd2dee4](https://github.com/Inist-CNRS/ezs/commit/dd2dee41ca9c127bb71a7ae6341708b253155c01))





## [1.12.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.12.0...@ezs/analytics@1.12.1) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))





# [1.12.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.11.2...@ezs/analytics@1.12.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





## [1.11.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.11.1...@ezs/analytics@1.11.2) (2021-02-13)


### Bug Fixes

* 🐛 stream overflowx ([d32fff1](https://github.com/Inist-CNRS/ezs/commit/d32fff147dfa32686df702bf8cd7aaa8d9c32079))





## [1.11.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.11.0...@ezs/analytics@1.11.1) (2021-01-29)

**Note:** Version bump only for package @ezs/analytics





# [1.11.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.10.1...@ezs/analytics@1.11.0) (2020-12-22)


### Features

* 🎸 remove useless method & statement ([d4000fb](https://github.com/Inist-CNRS/ezs/commit/d4000fb30f3720ed1c59019ab8c0d7cd1ceb6c4c))
* 🎸 tune [expand] sizex ([6a0825d](https://github.com/Inist-CNRS/ezs/commit/6a0825da528aa5ebafb339d8646569a058b27803))





## [1.10.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.10.0...@ezs/analytics@1.10.1) (2020-12-21)

**Note:** Version bump only for package @ezs/analytics





# [1.10.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.9.3...@ezs/analytics@1.10.0) (2020-12-17)


### Features

* [exchange] can use one pipeline per chunk (or not) ([c6fd334](https://github.com/Inist-CNRS/ezs/commit/c6fd334387753a3e6636f3ae37eb78e1bb6bde36))





## [1.9.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.9.2...@ezs/analytics@1.9.3) (2020-10-23)


### Bug Fixes

* 🐛 handle leaked ([c343c5c](https://github.com/Inist-CNRS/ezs/commit/c343c5c7949b3d063f65803130066934f96ce6c6))





## [1.9.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.9.1...@ezs/analytics@1.9.2) (2020-09-16)

**Note:** Version bump only for package @ezs/analytics





## [1.9.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.9.0...@ezs/analytics@1.9.1) (2020-09-14)

**Note:** Version bump only for package @ezs/analytics





# [1.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.8.0...@ezs/analytics@1.9.0) (2020-09-14)


### Bug Fixes

* catch async error ([924075d](https://github.com/Inist-CNRS/ezs/commit/924075d7786ad02cefa9c85a5f12add3193798cf))


### Features

* persistent option is better than cach option ([e66a2e3](https://github.com/Inist-CNRS/ezs/commit/e66a2e344d95cbf12c766b2003b44f456ffc939a))





# [1.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.7.2...@ezs/analytics@1.8.0) (2020-07-29)


### Features

* add array support to [combine] ([3cc2555](https://github.com/Inist-CNRS/ezs/commit/3cc2555c830e35a8101606d9cd29165ac0fe2189))





## [1.7.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.7.1...@ezs/analytics@1.7.2) (2020-07-27)

**Note:** Version bump only for package @ezs/analytics





## [1.7.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.7.0...@ezs/analytics@1.7.1) (2020-07-27)

**Note:** Version bump only for package @ezs/analytics





# [1.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.6.2...@ezs/analytics@1.7.0) (2020-07-27)


### Bug Fixes

* **analytics:** 🐛 attemps to fix combine too ([ad42c98](https://github.com/Inist-CNRS/ezs/commit/ad42c98e5a6a5cbf0c95b37b896fff920a3ee723))
* 🐛 fix push after end ([66750ed](https://github.com/Inist-CNRS/ezs/commit/66750edef28a6c6eebc8305da2bc16be16bcfd83))
* too many promises pending ([e9e708c](https://github.com/Inist-CNRS/ezs/commit/e9e708c3989550eb052d582b3414ecb98e88ebef))


### Features

* 🎸 add [expand] statement ([cc6a470](https://github.com/Inist-CNRS/ezs/commit/cc6a4707e445872c7cafd75dd55c920386ce9238))
* 🎸 attempts to fix travis bug ([6f31157](https://github.com/Inist-CNRS/ezs/commit/6f3115709fa0f9f59875103d45dcc8f65f102372))





## [1.6.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.6.1...@ezs/analytics@1.6.2) (2020-07-02)


### Bug Fixes

* 🐛 display status & message error ([2f67590](https://github.com/Inist-CNRS/ezs/commit/2f675904461e860eb2875513609a336209c46574))





## [1.6.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.6.0...@ezs/analytics@1.6.1) (2020-06-12)


### Bug Fixes

* 🐛 remainder of merge problem ([2020fb1](https://github.com/Inist-CNRS/ezs/commit/2020fb15d35141d5e2c0a2e9cf81568bc43b2382))





# [1.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.5.1...@ezs/analytics@1.6.0) (2020-06-12)


### Bug Fixes

* 🐛 remove useless chars ([ba5091a](https://github.com/Inist-CNRS/ezs/commit/ba5091ae8c1f488d438a294c9171142387311bb9))


### Features

* 🎸 add [combine] & [files] ([b4ee1e0](https://github.com/Inist-CNRS/ezs/commit/b4ee1e01e44385dba5520885e84aafaaa205b8b1))
* 🎸 new statement stash/unstash ([50ea3ad](https://github.com/Inist-CNRS/ezs/commit/50ea3ad198d0ef80c29715d9e0bdd28c1ab225a9))





## [1.5.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.5.0...@ezs/analytics@1.5.1) (2020-05-12)


### Bug Fixes

* 🐛 unable to disable cache from vars env ([1949927](https://github.com/Inist-CNRS/ezs/commit/1949927e65dd30a5ebf74139ceeb6138be4cdbcb))





# [1.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.4.3...@ezs/analytics@1.5.0) (2020-05-11)


### Features

* 🎸 improve : mode / cache / cli ([8ee1993](https://github.com/Inist-CNRS/ezs/commit/8ee1993724d71b0c0fe1fae9b3929a7dcb1693c5))





## [1.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.4.2...@ezs/analytics@1.4.3) (2020-04-27)

**Note:** Version bump only for package @ezs/analytics





## [1.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.4.1...@ezs/analytics@1.4.2) (2020-04-20)


### Bug Fixes

* 🐛 enable percentage ([0590713](https://github.com/Inist-CNRS/ezs/commit/0590713576a229ebcc1637709ed5a4b65b4da088))





## [1.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.4.0...@ezs/analytics@1.4.1) (2020-04-17)


### Bug Fixes

* 🐛 not existing keys ([7f9b5e7](https://github.com/Inist-CNRS/ezs/commit/7f9b5e7b21fe0d6f452b0dd2e6b2ecd51b5664fd))





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.3.4...@ezs/analytics@1.4.0) (2020-04-17)


### Features

* 🎸 add [statistics] statement ([85cbd9c](https://github.com/Inist-CNRS/ezs/commit/85cbd9c774d1ae5edd6224f73723d65639200086))





## [1.3.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.3.3...@ezs/analytics@1.3.4) (2020-04-06)


### Bug Fixes

* 🐛 security auditx ([92d7eec](https://github.com/Inist-CNRS/ezs/commit/92d7eecaad2ebd5b4ec600a33aa871c6bcf93112))





## [1.3.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.3.2...@ezs/analytics@1.3.3) (2020-03-31)

**Note:** Version bump only for package @ezs/analytics





## [1.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.3.1...@ezs/analytics@1.3.2) (2020-03-30)


### Bug Fixes

* 🐛 bug fixes ([277de15](https://github.com/Inist-CNRS/ezs/commit/277de15c1df537113cd5dfbe0f8a74470291770c))
* 🐛 ncu ([a05dcee](https://github.com/Inist-CNRS/ezs/commit/a05dcee3a8832a677706b8d0b30370f075785639))





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.3.0...@ezs/analytics@1.3.1) (2020-03-24)

**Note:** Version bump only for package @ezs/analytics





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.7...@ezs/analytics@1.3.0) (2020-03-23)


### Bug Fixes

* 🐛 fix tests ([aa53986](https://github.com/Inist-CNRS/ezs/commit/aa53986809f8b6088495e733e374d382dd0d8e32))


### Features

* 🎸 add statement [aggregate] ([399224d](https://github.com/Inist-CNRS/ezs/commit/399224d50550c67da0ac249cc5df88f759744329))


### Performance Improvements

* ⚡️ new statement [boost] ([65fe917](https://github.com/Inist-CNRS/ezs/commit/65fe917049f6804a4f26fa3c51c72c2a3d7ee6e6))
* ⚡️ new strategy to sort stream ([867be41](https://github.com/Inist-CNRS/ezs/commit/867be415c1300ff8ea52f5618e816c4347977c2a))





## [1.2.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.6...@ezs/analytics@1.2.7) (2020-02-27)

**Note:** Version bump only for package @ezs/analytics





## [1.2.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.5...@ezs/analytics@1.2.6) (2020-02-26)

**Note:** Version bump only for package @ezs/analytics





## [1.2.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.4...@ezs/analytics@1.2.5) (2020-02-03)

**Note:** Version bump only for package @ezs/analytics





## [1.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.2...@ezs/analytics@1.2.4) (2020-01-10)


### Bug Fixes

* 🐛 start parameter should be use to compute the size ([bf87f52](https://github.com/Inist-CNRS/ezs/commit/bf87f525e40206e68e139dc14e964f7a96e18ab6))





## [1.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.2...@ezs/analytics@1.2.3) (2020-01-10)


### Bug Fixes

* 🐛 start parameter should be use to compute the size ([bf87f52](https://github.com/Inist-CNRS/ezs/commit/bf87f525e40206e68e139dc14e964f7a96e18ab6))





## [1.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.1...@ezs/analytics@1.2.2) (2019-12-20)

**Note:** Version bump only for package @ezs/analytics





## [1.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.2.0...@ezs/analytics@1.2.1) (2019-12-14)


### Bug Fixes

* 🐛 do not convert number to stringx ([aeb1947](https://github.com/Inist-CNRS/ezs/commit/aeb1947e73996edbaf5f5038a41f9e7bc4ee9212))





# [1.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.1.0...@ezs/analytics@1.2.0) (2019-12-14)


### Features

* 🎸 new statement [distance] ([4d6162a](https://github.com/Inist-CNRS/ezs/commit/4d6162a4f5927e3cf582712301a723d019863686))





# [1.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.10...@ezs/analytics@1.1.0) (2019-12-13)


### Features

* 🎸 new statement [multiply] ([145a8ef](https://github.com/Inist-CNRS/ezs/commit/145a8ef6a913c57a11624c308949552d43c3dfb1))





## [1.0.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.9...@ezs/analytics@1.0.10) (2019-11-28)


### Bug Fixes

* 🐛 drop statement ignores parameter when it's equal to 0 ([67be481](https://github.com/Inist-CNRS/ezs/commit/67be4815db210e139ced50819012b467e03c580a))





## [1.0.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.8...@ezs/analytics@1.0.9) (2019-11-07)

**Note:** Version bump only for package @ezs/analytics





## [1.0.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.7...@ezs/analytics@1.0.8) (2019-11-02)

**Note:** Version bump only for package @ezs/analytics





## [1.0.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.6...@ezs/analytics@1.0.7) (2019-10-25)

**Note:** Version bump only for package @ezs/analytics





## [1.0.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.5...@ezs/analytics@1.0.6) (2019-09-28)

**Note:** Version bump only for package @ezs/analytics





## [1.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.4...@ezs/analytics@1.0.5) (2019-09-13)

**Note:** Version bump only for package @ezs/analytics





## [1.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.3...@ezs/analytics@1.0.4) (2019-09-13)

**Note:** Version bump only for package @ezs/analytics





## [1.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.2...@ezs/analytics@1.0.3) (2019-09-11)

**Note:** Version bump only for package @ezs/analytics





## [1.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.1...@ezs/analytics@1.0.2) (2019-09-09)

**Note:** Version bump only for package @ezs/analytics





## [1.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/analytics@1.0.0...@ezs/analytics@1.0.1) (2019-09-06)

**Note:** Version bump only for package @ezs/analytics





# 1.0.0 (2019-09-06)


### Build System

* **analytics:** Adapt package.json ([5c21849](https://github.com/Inist-CNRS/ezs/commit/5c21849))


### BREAKING CHANGES

* **analytics:** Because I want a first release at 1.0.0
