# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [4.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.1.0...@ezs/core@4.1.1) (2025-09-19)


### Bug Fixes

* 🐛 error with no option ([474fed5](https://github.com/Inist-CNRS/ezs/commit/474fed5a0cf21eca139b37c71ccc5126809352b5))





# [4.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.0.5...@ezs/core@4.1.0) (2025-09-15)


### Features

* 🎸 add [catch] ([69216d8](https://github.com/Inist-CNRS/ezs/commit/69216d8e8e22120e5279e52ca1bfd5d5010ba3fb))





## [4.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.0.4...@ezs/core@4.0.5) (2025-09-12)


### Bug Fixes

* 🐛 missing one use case ([e20210a](https://github.com/Inist-CNRS/ezs/commit/e20210a3d4e8bdf4c50f4e43e0dc0d842fcb643b))





## [4.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.0.3...@ezs/core@4.0.4) (2025-09-12)


### Bug Fixes

* 🐛 [detach] with CJS ([6f83e65](https://github.com/Inist-CNRS/ezs/commit/6f83e65dba612fb4d27523c49cdb64490807140b))
* 🐛 cjs cannot import module ([6559ee8](https://github.com/Inist-CNRS/ezs/commit/6559ee8c623d0ac5f8449825f1b61c771a3da9db))





## [4.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.0.2...@ezs/core@4.0.3) (2025-09-09)


### Bug Fixes

* 🐛 allow the use of some internal modules ([4c9bc42](https://github.com/Inist-CNRS/ezs/commit/4c9bc4294a4c13502c0873fc60411c2b4d0d5f33))





## [4.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.0.1...@ezs/core@4.0.2) (2025-09-08)


### Bug Fixes

* 🐛 The require function does not exist in esm ([d74b2b4](https://github.com/Inist-CNRS/ezs/commit/d74b2b4e348490c449ccdc7a1c353259fdab62c4))





## [4.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@4.0.0...@ezs/core@4.0.1) (2025-09-02)

**Note:** Version bump only for package @ezs/core





# [4.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.11.2...@ezs/core@4.0.0) (2025-08-29)


### Bug Fixes

* 🐛 bug with less data than the concurrency size ([21dd1f8](https://github.com/Inist-CNRS/ezs/commit/21dd1f8cfa573baff572e8340a18df4f49910158))
* 🐛 dynamic importx ([04ffe17](https://github.com/Inist-CNRS/ezs/commit/04ffe176b6e0e118cd7a3c1aa3be23304d8c6923))
* 🐛 fix json import for node 12 to 16 ([501becb](https://github.com/Inist-CNRS/ezs/commit/501becbb0dfff5b4a49b12006e0832603d97e669))
* 🐛 tests ([cf96dd0](https://github.com/Inist-CNRS/ezs/commit/cf96dd09ee14c5ea983b548a145e490b5de2a5da))
* 🐛 tests ([e08ece1](https://github.com/Inist-CNRS/ezs/commit/e08ece1efa6f5b826dad0149ee7d264f5a3c9dbb))
* 🐛 timeout for workers ([99eb0ab](https://github.com/Inist-CNRS/ezs/commit/99eb0abb6f0198e417429cce59d0c1f7c92d6747))
* 🐛 transfert plugins list ([5fcdb27](https://github.com/Inist-CNRS/ezs/commit/5fcdb27bebd0f48756178d87d69fd4337e4077ec))
* 🐛 use settings to store plugin paths ([1035ed3](https://github.com/Inist-CNRS/ezs/commit/1035ed3c84d034e4eb2fa55a490cc97f1c990990))
* 🐛 worker_threads impose esm by default ([52875c9](https://github.com/Inist-CNRS/ezs/commit/52875c9957cab43f63e0731e5ccaa735da139f33))


### Features

* 🎸 add [detach] ([107b032](https://github.com/Inist-CNRS/ezs/commit/107b0321a987ecdfb0f1746f9739fdb66cc9ccd7))
* 🎸 new way to choose the main statement ([4b8e816](https://github.com/Inist-CNRS/ezs/commit/4b8e816a25b65e84e9186c0f1eec13097d02dda7))
* add "path=" paramter to [group] and [ungroup] ([24feb2f](https://github.com/Inist-CNRS/ezs/commit/24feb2f1b203befef743257d6df1a7d71bd0f187))
* add "path=" to [pack] and [unpack] ([45b8b5e](https://github.com/Inist-CNRS/ezs/commit/45b8b5e3c229a623d957a7acb25a71ac386e1295))


### Performance Improvements

* ⚡️ to avoid displaying buffers ([76bead2](https://github.com/Inist-CNRS/ezs/commit/76bead2933cbf017e6c821d8840ea4db9506de41))


### BREAKING CHANGES

* 🧨 type:module





## [3.11.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.11.1...@ezs/core@3.11.2) (2025-05-26)


### Bug Fixes

* 🐛 add log for empty result ([a2c8c8f](https://github.com/Inist-CNRS/ezs/commit/a2c8c8fb4ac1a175c387e72a1faa2083e4449065))
* 🐛 unit tests ([4db05a8](https://github.com/Inist-CNRS/ezs/commit/4db05a819ba6681ea306a185f22d3442ab07bd73))





## [3.11.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.11.0...@ezs/core@3.11.1) (2025-03-03)

**Note:** Version bump only for package @ezs/core





# [3.11.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.10...@ezs/core@3.11.0) (2025-01-21)


### Features

* 🎸 drop useless feature ([0a55722](https://github.com/Inist-CNRS/ezs/commit/0a55722846605f5e07fbcb5ec9eec0dc9250b9ef))





## [3.10.10](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.9...@ezs/core@3.10.10) (2024-12-13)


### Bug Fixes

* 🐛 avoid lockx ([0959106](https://github.com/Inist-CNRS/ezs/commit/09591061edb3a6fad309128de11f8c6dbb93da24))





## [3.10.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.8...@ezs/core@3.10.9) (2024-11-29)

**Note:** Version bump only for package @ezs/core





## [3.10.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.7...@ezs/core@3.10.8) (2024-11-22)

**Note:** Version bump only for package @ezs/core





## [3.10.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.6...@ezs/core@3.10.7) (2024-11-08)

**Note:** Version bump only for package @ezs/core





## [3.10.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.5...@ezs/core@3.10.6) (2024-11-07)


### Bug Fixes

* 🐛 avoid server crash ([5c9c7ad](https://github.com/Inist-CNRS/ezs/commit/5c9c7ad597412a4226465c9ea2b99ddbbb343393))





## [3.10.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.4...@ezs/core@3.10.5) (2024-11-05)

**Note:** Version bump only for package @ezs/core





## [3.10.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.3...@ezs/core@3.10.4) (2024-09-18)

**Note:** Version bump only for package @ezs/core





## [3.10.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.2...@ezs/core@3.10.3) (2024-08-02)

**Note:** Version bump only for package @ezs/core





## [3.10.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.1...@ezs/core@3.10.2) (2024-07-04)

**Note:** Version bump only for package @ezs/core





## [3.10.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.10.0...@ezs/core@3.10.1) (2024-06-19)


### Bug Fixes

* 🐛 deleting a global variable is diabolical ([8936dc0](https://github.com/Inist-CNRS/ezs/commit/8936dc075ab73ca32234f758a36a37247e644ab7))





# [3.10.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.9.0...@ezs/core@3.10.0) (2024-06-07)


### Bug Fixes

* 🐛 async requests should have their own request ID ([2fb6003](https://github.com/Inist-CNRS/ezs/commit/2fb6003ae2f7347a7d2468e0029b7e841f221066))
* 🐛 remove functionx ([e9b9b71](https://github.com/Inist-CNRS/ezs/commit/e9b9b715058983eba349334da99eb9d1a7ef8dec))
* 🐛 remove useless file ([5d59d9b](https://github.com/Inist-CNRS/ezs/commit/5d59d9b1a6edd0362821ec8ac50624acaa68ad58))
* 🐛 remove useless files ([000dbbc](https://github.com/Inist-CNRS/ezs/commit/000dbbccf2fefddaa19647b56c8a4943a93c8e5a))


### Features

* 🎸 drop useless rpc mode ([9460ad0](https://github.com/Inist-CNRS/ezs/commit/9460ad0c8c8ec92f51125014fa197f805e9a4e9f))





# [3.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.7...@ezs/core@3.9.0) (2024-05-31)


### Bug Fixes

* 🐛 tests ([2205210](https://github.com/Inist-CNRS/ezs/commit/220521034c2055488a1e7d8349b15ae0aa5461b7))
* 🐛 wrong type ([95de130](https://github.com/Inist-CNRS/ezs/commit/95de130b8260ae3b985c1eb63a3389ec1573d8f1))


### Features

* 🎸 add [breaker] ([54d566c](https://github.com/Inist-CNRS/ezs/commit/54d566ce0aaf1125744c91e659e82db549046664))
* 🎸 add x-request-id ([089d690](https://github.com/Inist-CNRS/ezs/commit/089d690834e4dcdfb35e26161f89438e3c329fd6))





## [3.8.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.6...@ezs/core@3.8.7) (2024-05-30)

**Note:** Version bump only for package @ezs/core





## [3.8.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.5...@ezs/core@3.8.6) (2024-05-29)


### Bug Fixes

* 🐛 circular ref ([66cabad](https://github.com/Inist-CNRS/ezs/commit/66cabad0d0be031765bc498e80c5946ffe564b0c))





## [3.8.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.4...@ezs/core@3.8.5) (2024-05-17)


### Bug Fixes

* 🐛 avoid useless debug message ([a714970](https://github.com/Inist-CNRS/ezs/commit/a7149704ff2254f6ff6c4893aeca89a2a0945850))
* 🐛 use specific debug message for each error ([503f2ab](https://github.com/Inist-CNRS/ezs/commit/503f2ab65a31ab818d418b59c8caf97df1190463))





## [3.8.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.3...@ezs/core@3.8.4) (2024-04-25)

**Note:** Version bump only for package @ezs/core





## [3.8.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.2...@ezs/core@3.8.3) (2024-04-10)


### Bug Fixes

* 🐛 hyphenation for dos filex ([6a6bcbc](https://github.com/Inist-CNRS/ezs/commit/6a6bcbcc9da717f2561a5f956bcab11a3594149e))





## [3.8.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.1...@ezs/core@3.8.2) (2024-03-25)


### Bug Fixes

* 🐛 add autoclose option for feed.flow ([0fc3ad2](https://github.com/Inist-CNRS/ezs/commit/0fc3ad2db7118dc51df35f60ee125ee35097ed96))
* 🐛 autoClose is set to off by default ([6e6810d](https://github.com/Inist-CNRS/ezs/commit/6e6810d7336e428a40aa46d6c12b24fc2d2231f5))
* 🐛 deliberate premature closure ([5e807e0](https://github.com/Inist-CNRS/ezs/commit/5e807e0159e491134811fabe1b5a81d6d8edb871))





## [3.8.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.8.0...@ezs/core@3.8.1) (2024-02-10)


### Bug Fixes

* 🐛 clean memory after each flow ([07f46ad](https://github.com/Inist-CNRS/ezs/commit/07f46add096f7d604b718fee4d58fe532b7d2191))





# [3.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.7.0...@ezs/core@3.8.0) (2024-02-09)


### Bug Fixes

* 🐛 improve tests ([fbeb531](https://github.com/Inist-CNRS/ezs/commit/fbeb53184028cacf3fc9ac6fa0096601f76aeded))


### Features

* 🎸 cacheName for combine like expand ([f79a52a](https://github.com/Inist-CNRS/ezs/commit/f79a52a028b287d7fc6fc0d566ba7bf8f728a436))





# [3.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.6.0...@ezs/core@3.7.0) (2024-02-07)


### Features

* 🎸 add identifier param (like [graph]) ([855916a](https://github.com/Inist-CNRS/ezs/commit/855916aff4a8ae6fc3b68e6c06435b4939ced812))





# [3.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.5.0...@ezs/core@3.6.0) (2024-01-26)


### Features

* 🎸 add [BIBParse] ([20489b8](https://github.com/Inist-CNRS/ezs/commit/20489b8c816edc7e504c81e836ae558b7a524bcf))





# [3.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.4.4...@ezs/core@3.5.0) (2023-12-20)


### Features

* allow multilines metadata values ([8f342f1](https://github.com/Inist-CNRS/ezs/commit/8f342f11ca4fb67aef44c8d76f4dba68ce891b32))





## [3.4.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.4.3...@ezs/core@3.4.4) (2023-12-19)


### Bug Fixes

* 🐛 with undefined data ([12b14c0](https://github.com/Inist-CNRS/ezs/commit/12b14c0ffa28f3931550a827f4f66e05557498ca))





## [3.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.4.2...@ezs/core@3.4.3) (2023-12-19)


### Bug Fixes

* 🐛 sha with whole data ([f155a6b](https://github.com/Inist-CNRS/ezs/commit/f155a6b0434f6700606a291e51094cb284c3e33f))





## [3.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.4.1...@ezs/core@3.4.2) (2023-12-08)

**Note:** Version bump only for package @ezs/core





## [3.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.4.0...@ezs/core@3.4.1) (2023-12-08)

**Note:** Version bump only for package @ezs/core





# [3.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.3.1...@ezs/core@3.4.0) (2023-11-27)


### Features

* 🎸 add [dedupe] ([86c06f2](https://github.com/Inist-CNRS/ezs/commit/86c06f297a0eaa2216352f255137b2cff0eabf28))





## [3.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.3.0...@ezs/core@3.3.1) (2023-11-23)


### Bug Fixes

* 🐛 add tracback in error message ([3b27b8d](https://github.com/Inist-CNRS/ezs/commit/3b27b8d160d8aba5c2c08e2400e7aff85c9def84))





# [3.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.2.2...@ezs/core@3.3.0) (2023-11-21)


### Features

* 🎸 enable [metrics] for all ([98cb0c3](https://github.com/Inist-CNRS/ezs/commit/98cb0c32eea6dbe17c765e077c4f1c1ab1ab1d4f))





## [3.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.2.1...@ezs/core@3.2.2) (2023-11-12)


### Bug Fixes

* 🐛 invalid header with error ([7ee5869](https://github.com/Inist-CNRS/ezs/commit/7ee5869729504ed116d84f3e7781c5d88264487c))





## [3.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.2.0...@ezs/core@3.2.1) (2023-10-26)


### Bug Fixes

* 🐛 attempts to fix lost body with proxy ([671030d](https://github.com/Inist-CNRS/ezs/commit/671030da34a32752bca1e551579fa559f1182588))





# [3.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.1.3...@ezs/core@3.2.0) (2023-10-25)


### Bug Fixes

* 🐛 json error bodyresponse ([4ad1789](https://github.com/Inist-CNRS/ezs/commit/4ad17899d12bac30d8ec4c25d01da63301ef7165))


### Features

* 🎸 improve error message display ([66ea68a](https://github.com/Inist-CNRS/ezs/commit/66ea68a383840a837df6da516bae02aa538ee9dd))





## [3.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.1.2...@ezs/core@3.1.3) (2023-10-25)


### Bug Fixes

* 🐛 error status code ([dea909f](https://github.com/Inist-CNRS/ezs/commit/dea909f2beba600ea27387510ab33a44174e0f6a))





## [3.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.1.1...@ezs/core@3.1.2) (2023-10-20)


### Bug Fixes

* 🐛 allow swagger ui to download binary file ([080c150](https://github.com/Inist-CNRS/ezs/commit/080c150f00325e28092a659d7eef1a2771ef070a))





## [3.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.1.0...@ezs/core@3.1.1) (2023-10-20)


### Bug Fixes

* 🐛 attachment header ([0399d6d](https://github.com/Inist-CNRS/ezs/commit/0399d6d24029b7f4c53e8f0e40369bbf62418d7f))





# [3.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.7...@ezs/core@3.1.0) (2023-10-13)


### Bug Fixes

* 🐛 avoid to write in closed stream ([27df67a](https://github.com/Inist-CNRS/ezs/commit/27df67ab9ac74eb0f8d5193cead5c8ea898dae88))
* 🐛 best errors control in standalone mode ([8e74dfc](https://github.com/Inist-CNRS/ezs/commit/8e74dfc696eb557d0777877734b6a31378c2d0b3))


### Features

* 🎸 add trap system for externals pipelines ([3e71a4d](https://github.com/Inist-CNRS/ezs/commit/3e71a4dd6de90dfd46ff97d6bad64ac15da97a46))





## [3.0.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.6...@ezs/core@3.0.7) (2023-09-29)


### Bug Fixes

* 🐛 missing string decoder ([e9461e1](https://github.com/Inist-CNRS/ezs/commit/e9461e17a9125a681a1988d5f0ecf007abb4f5ef))





## [3.0.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.5...@ezs/core@3.0.6) (2023-09-08)

**Note:** Version bump only for package @ezs/core





## [3.0.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.4...@ezs/core@3.0.5) (2023-08-18)

**Note:** Version bump only for package @ezs/core





## [3.0.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.3...@ezs/core@3.0.4) (2023-07-20)


### Bug Fixes

* 🐛 manifest.json ([559cda8](https://github.com/Inist-CNRS/ezs/commit/559cda8372c65c3c1cfb5a0dcfee404ece4a79d6))





## [3.0.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.2...@ezs/core@3.0.3) (2023-07-17)


### Bug Fixes

* 🐛 add debug information to unpack statement ([512a128](https://github.com/Inist-CNRS/ezs/commit/512a128e9de5f35ad88b80b44d358356d03eeb6e))





## [3.0.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.1...@ezs/core@3.0.2) (2023-06-23)

**Note:** Version bump only for package @ezs/core





## [3.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@3.0.0...@ezs/core@3.0.1) (2023-06-22)

**Note:** Version bump only for package @ezs/core





# [3.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.5.4...@ezs/core@3.0.0) (2023-06-14)


### Features

* 🎸 add a note to keep the most relevant element ([1d867a9](https://github.com/Inist-CNRS/ezs/commit/1d867a963d06a5ba9bd64a46a92173f4544b32b9))
* 🎸 change store engine ([10494c9](https://github.com/Inist-CNRS/ezs/commit/10494c93d2ac021dcd8c5b7929b4045f6b8b0835))


### BREAKING CHANGES

* 🧨 possibly





## [2.5.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.5.3...@ezs/core@2.5.4) (2023-05-16)

**Note:** Version bump only for package @ezs/core





## [2.5.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.5.2...@ezs/core@2.5.3) (2023-05-12)

**Note:** Version bump only for package @ezs/core





## [2.5.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.5.1...@ezs/core@2.5.2) (2023-04-12)

**Note:** Version bump only for package @ezs/core





## [2.5.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.5.0...@ezs/core@2.5.1) (2023-03-28)

**Note:** Version bump only for package @ezs/core





# [2.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.4.3...@ezs/core@2.5.0) (2023-03-24)


### Bug Fixes

* 🐛 fix tests ([155fb77](https://github.com/Inist-CNRS/ezs/commit/155fb77e2ba1bd0ebdd38b65ca7119e7a1775f87))
* 🐛 map use all values as an array ([c8268e3](https://github.com/Inist-CNRS/ezs/commit/c8268e3ea1a7efbd8ca841ec20d3bd352a2faaf5))
* 🐛 useless code ([8b90913](https://github.com/Inist-CNRS/ezs/commit/8b90913eeea83aee17ca54856c76194eeab76945))
* 🐛 valid id is too loose ([86cc6d7](https://github.com/Inist-CNRS/ezs/commit/86cc6d77c8697d26a79ab14fc148806ca3f20454))


### Features

* 🎸 add [fork] ([651aeda](https://github.com/Inist-CNRS/ezs/commit/651aedadd4210bd3c93e2d609dd42fde718511fc))





## [2.4.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.4.2...@ezs/core@2.4.3) (2023-03-07)

**Note:** Version bump only for package @ezs/core





## [2.4.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.4.1...@ezs/core@2.4.2) (2023-02-27)


### Bug Fixes

* 🐛 [swing] could never be true ([35c48ca](https://github.com/Inist-CNRS/ezs/commit/35c48ca3f2be5796b5e14391b306b28fd9d5c697))
* 🐛 fetch timeout vs feed timeout ([163e0bf](https://github.com/Inist-CNRS/ezs/commit/163e0bf81df5f3d8ef7d2fedc6b8bf95ec2c8534))





## [2.4.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.4.0...@ezs/core@2.4.1) (2023-02-17)


### Bug Fixes

* 🐛 avoid to ignore errors in pipeline ([8ec4703](https://github.com/Inist-CNRS/ezs/commit/8ec4703c5b35329cc125e6b2ff914402f00609a3))
* 🐛 timeout never closed ([0c8597a](https://github.com/Inist-CNRS/ezs/commit/0c8597a11db0c3689a34888a1fd3e7807cc975db))





# [2.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.3.2...@ezs/core@2.4.0) (2023-02-15)


### Bug Fixes

* 🐛 increase default value ([9728772](https://github.com/Inist-CNRS/ezs/commit/9728772c1c5b24c8677f82b77a452cdc13814364))
* wrong function name ([0cfd336](https://github.com/Inist-CNRS/ezs/commit/0cfd336588d8cac9c7c10d96442cc72ac71d21c2))


### Features

* 🎸 add timeout on sub pipeline ([57e0707](https://github.com/Inist-CNRS/ezs/commit/57e07070ba21cb3cb105887277d9466645997dbe))





## [2.3.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.3.1...@ezs/core@2.3.2) (2023-02-08)

**Note:** Version bump only for package @ezs/core





## [2.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.3.0...@ezs/core@2.3.1) (2023-01-28)


### Bug Fixes

* change default name (printed on error) ([65d591a](https://github.com/Inist-CNRS/ezs/commit/65d591a4e9ec252ea042328fbd7bac562e175a84))


### Reverts

* Revert "chore: 🤖 use lerna exec instead bootstrap" ([56375ee](https://github.com/Inist-CNRS/ezs/commit/56375ee2bd7e9f69f61da3993ab569ca1c16c547))





# [2.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.2.1...@ezs/core@2.3.0) (2023-01-25)


### Bug Fixes

* add missing file ([8a687e8](https://github.com/Inist-CNRS/ezs/commit/8a687e8820a02e1398c5ec1b9321506ad37e2aa9))
* missing package ([1082dd8](https://github.com/Inist-CNRS/ezs/commit/1082dd8e9e8dbdbc10be796bfb48beb8f219377f))


### Features

* 🎸 add [pop] ([9f1235a](https://github.com/Inist-CNRS/ezs/commit/9f1235adc6da35b760c17da9f2531034687f04ad))





## [2.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.2.0...@ezs/core@2.2.1) (2023-01-20)

**Note:** Version bump only for package @ezs/core





# [2.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.9...@ezs/core@2.2.0) (2023-01-13)


### Features

* 🎸 support sha ([5ed40ee](https://github.com/Inist-CNRS/ezs/commit/5ed40eee646ff07018dba0cd87ce5915fd06f953))





## [2.1.9](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.8...@ezs/core@2.1.9) (2023-01-05)

**Note:** Version bump only for package @ezs/core





## [2.1.8](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.7...@ezs/core@2.1.8) (2022-12-22)


### Bug Fixes

* 🐛 debug again and again ([d1fe2c0](https://github.com/Inist-CNRS/ezs/commit/d1fe2c0d2282b25dbe74dcbfa56d83d39d116228))





## [2.1.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.6...@ezs/core@2.1.7) (2022-12-22)


### Bug Fixes

* 🐛 now, it's work like explained in the doc ([7c2b966](https://github.com/Inist-CNRS/ezs/commit/7c2b966791dca7b43c62d4934aa1614027f8a01d))





## [2.1.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.5...@ezs/core@2.1.6) (2022-12-22)


### Bug Fixes

* 🐛 debug trace parameterx ([1b0b124](https://github.com/Inist-CNRS/ezs/commit/1b0b124d9077f54ca93c40357cc4d1d19090ba7b))





## [2.1.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.4...@ezs/core@2.1.5) (2022-12-22)


### Bug Fixes

* 🐛 improve debug statement (bis) ([4daa267](https://github.com/Inist-CNRS/ezs/commit/4daa267ae93a2b0842e2371f2aad824207c2c53c))





## [2.1.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.3...@ezs/core@2.1.4) (2022-12-21)


### Bug Fixes

* 🐛 improve debug statement ([31bb60d](https://github.com/Inist-CNRS/ezs/commit/31bb60d7fef3de5446853ae73e7c331a95c1d5e4))





## [2.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.2...@ezs/core@2.1.3) (2022-12-14)


### Bug Fixes

* 🐛 DELAY env vars more explicit ([c162be8](https://github.com/Inist-CNRS/ezs/commit/c162be8509d8f1ec7a37a63048a7138bd9573406))





## [2.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.1...@ezs/core@2.1.2) (2022-12-02)

**Note:** Version bump only for package @ezs/core





## [2.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.1.0...@ezs/core@2.1.1) (2022-11-28)


### Bug Fixes

* [expand] losing some batches ([bf29c38](https://github.com/Inist-CNRS/ezs/commit/bf29c382d79bc65f0524831447935fdbe120dd37))
* losing some batches ([4e21668](https://github.com/Inist-CNRS/ezs/commit/4e21668c4db451a214b15f3777f921ca6f1a97ec))





# [2.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.0.1...@ezs/core@2.1.0) (2022-09-21)


### Features

* 🎸 add [overturn] ([54e3052](https://github.com/Inist-CNRS/ezs/commit/54e30528cd1c7014becce96d96eb1273f10c961a))





## [2.0.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@2.0.0...@ezs/core@2.0.1) (2022-09-19)


### Bug Fixes

* 🐛 locations like [files] ([42fb184](https://github.com/Inist-CNRS/ezs/commit/42fb184bba3534bf2123a12d276d89f6809d09a7))





# [2.0.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.30.2...@ezs/core@2.0.0) (2022-09-16)


### Code Refactoring

* 💡 store.close() ([34fa4c3](https://github.com/Inist-CNRS/ezs/commit/34fa4c3b9af943c7523f5dc8926567e2401ee669))


### Features

* 🎸 move [expand] & [combine] to core ([1b97856](https://github.com/Inist-CNRS/ezs/commit/1b97856bbff4008388253dad273653d52af1c87f))
* 🎸 move [identify] to core ([dea845c](https://github.com/Inist-CNRS/ezs/commit/dea845ccd16575edeab62df709ba2756e332f5c1))


### BREAKING CHANGES

* 🧨 drop [files] [uplaod] [buffers] [bufferize]





## [1.30.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.30.1...@ezs/core@1.30.2) (2022-09-14)


### Bug Fixes

* 🐛 feed.flow return a Promise ([e73f140](https://github.com/Inist-CNRS/ezs/commit/e73f14042eb60b464c9e021345e479d24ba0ec81))





## [1.30.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.30.0...@ezs/core@1.30.1) (2022-09-08)


### Bug Fixes

* 🐛 fix deadlock ([9835fc4](https://github.com/Inist-CNRS/ezs/commit/9835fc472dbec5b016228b15230af0f6bbe60bae))





# [1.30.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.29.1...@ezs/core@1.30.0) (2022-09-06)


### Bug Fixes

* 🐛 use promise ([9851782](https://github.com/Inist-CNRS/ezs/commit/9851782c8a2c5ab8ece85eb8a88ca883be09d227))


### Features

* 🎸 add [map] ([da4cf8f](https://github.com/Inist-CNRS/ezs/commit/da4cf8fa2d6b11bf65273131e5b32f8364b2fe4a))





## [1.29.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.29.0...@ezs/core@1.29.1) (2022-09-04)

**Note:** Version bump only for package @ezs/core





# [1.29.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.28.3...@ezs/core@1.29.0) (2022-06-30)


### Bug Fixes

* 🐛 looping looks good ([378b024](https://github.com/Inist-CNRS/ezs/commit/378b024aac1f73e6b21f31054441257d02c83d41))


### Features

* 🎸 add [loop] ([cb190ac](https://github.com/Inist-CNRS/ezs/commit/cb190ac3da58b322096e4c3b6452b9d037384828))





## [1.28.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.28.2...@ezs/core@1.28.3) (2022-06-21)

**Note:** Version bump only for package @ezs/core





## [1.28.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.28.1...@ezs/core@1.28.2) (2022-04-02)


### Bug Fixes

* 🐛 erratic error ([cf15306](https://github.com/Inist-CNRS/ezs/commit/cf15306531070939295ff8fa922795e1fd7a2fb2))





## [1.28.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.28.0...@ezs/core@1.28.1) (2022-03-20)

**Note:** Version bump only for package @ezs/core





# [1.28.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.27.4...@ezs/core@1.28.0) (2022-02-07)


### Features

* 🎸 module.exports for all packages ([086a289](https://github.com/Inist-CNRS/ezs/commit/086a289ccbaa5c72ee7bc6652ab3c6c6b5578138))





## [1.27.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.27.3...@ezs/core@1.27.4) (2022-02-04)


### Bug Fixes

* 🐛 normalize arrow func usage ([e99603d](https://github.com/Inist-CNRS/ezs/commit/e99603dca52b143dde3d179ca15036d1116c50b4))





## [1.27.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.27.2...@ezs/core@1.27.3) (2022-01-31)

**Note:** Version bump only for package @ezs/core





## [1.27.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.27.1...@ezs/core@1.27.2) (2022-01-31)


### Bug Fixes

* 🐛 add warning for back pressure control ([beb67f8](https://github.com/Inist-CNRS/ezs/commit/beb67f837ac4589221d0a2bf4192f7e3ca9b25d3))





## [1.27.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.27.0...@ezs/core@1.27.1) (2022-01-27)

**Note:** Version bump only for package @ezs/core





# [1.27.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.26.3...@ezs/core@1.27.0) (2022-01-19)


### Features

* 🎸 add sourceChunk in Error ([7ae43e7](https://github.com/Inist-CNRS/ezs/commit/7ae43e7254c4c45fc53211ec05a1fb6a176874af))





## [1.26.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.26.2...@ezs/core@1.26.3) (2022-01-06)


### Bug Fixes

* 🐛 deep nested script ([627ca49](https://github.com/Inist-CNRS/ezs/commit/627ca49dadcc3a34e06f7cd44dd2861080c6c1ea))





## [1.26.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.26.1...@ezs/core@1.26.2) (2021-12-23)

**Note:** Version bump only for package @ezs/core





## [1.26.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.26.0...@ezs/core@1.26.1) (2021-10-29)

**Note:** Version bump only for package @ezs/core





# [1.26.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.25.3...@ezs/core@1.26.0) (2021-10-03)


### Bug Fixes

* 🐛 iattempts to fix tests ([48168ee](https://github.com/Inist-CNRS/ezs/commit/48168eee983f16fccd2dee93950bd27baa1c4aa3))


### Features

* 🎸 use prom-client ([3ad4548](https://github.com/Inist-CNRS/ezs/commit/3ad4548e68c040fd7328dc35a8c8f5425bb36d1b))





## [1.25.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.25.2...@ezs/core@1.25.3) (2021-09-27)


### Bug Fixes

* 🐛 enable metrics for cli ([05d110a](https://github.com/Inist-CNRS/ezs/commit/05d110ab2523ebee9c62ffc6af8575340a89a9f1))
* 🐛 headers for CORS ([feec292](https://github.com/Inist-CNRS/ezs/commit/feec292566b619077a341ba8b175729d8b26f091))
* 🐛 improve stage label ([5cc5d72](https://github.com/Inist-CNRS/ezs/commit/5cc5d72d8da481456ce4044b9c07763e32d64d0f))


### Performance Improvements

* ⚡️ add new metrics ([c2e3a2d](https://github.com/Inist-CNRS/ezs/commit/c2e3a2d7dbea06dec569061744bcb0a0255a0644))





## [1.25.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.25.1...@ezs/core@1.25.2) (2021-09-17)


### Bug Fixes

* 🐛 swagger-ui & cors ([2e920be](https://github.com/Inist-CNRS/ezs/commit/2e920be1961b104dc7695ec33e98117b1d395af6))





## [1.25.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.25.0...@ezs/core@1.25.1) (2021-09-03)

**Note:** Version bump only for package @ezs/core





# [1.25.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.24.4...@ezs/core@1.25.0) (2021-08-31)


### Bug Fixes

* env() use process.env by default ([b742448](https://github.com/Inist-CNRS/ezs/commit/b742448ebc0315e9b4e9e834724390068732511b))


### Features

* 🎸 add preprend() & append() mixins ([c418bcc](https://github.com/Inist-CNRS/ezs/commit/c418bcc70270b576e5ca8e5d836efa577ce065e8))





## [1.24.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.24.3...@ezs/core@1.24.4) (2021-07-30)

**Note:** Version bump only for package @ezs/core





## [1.24.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.24.2...@ezs/core@1.24.3) (2021-07-22)

**Note:** Version bump only for package @ezs/core





## [1.24.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.24.1...@ezs/core@1.24.2) (2021-07-15)

**Note:** Version bump only for package @ezs/core





## [1.24.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.24.0...@ezs/core@1.24.1) (2021-06-28)

**Note:** Version bump only for package @ezs/core





# [1.24.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.7...@ezs/core@1.24.0) (2021-06-04)


### Features

* Specified Aggregation query for sub resource ([b594c95](https://github.com/Inist-CNRS/ezs/commit/b594c952b5baa57c818d62f4e9cf6d25d4bd1c7a))





## [1.23.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.6...@ezs/core@1.23.7) (2021-05-05)


### Bug Fixes

* 🐛 remove sync code ([0a63cef](https://github.com/Inist-CNRS/ezs/commit/0a63cef73bd0ce854a8e3f0e2d2306f3ecf0b158))





## [1.23.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.5...@ezs/core@1.23.6) (2021-04-26)

**Note:** Version bump only for package @ezs/core





## [1.23.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.4...@ezs/core@1.23.5) (2021-04-20)

**Note:** Version bump only for package @ezs/core





## [1.23.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.3...@ezs/core@1.23.4) (2021-04-20)

**Note:** Version bump only for package @ezs/core





## [1.23.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.2...@ezs/core@1.23.3) (2021-04-09)


### Bug Fixes

* 🐛 unpack crash ([326c6a0](https://github.com/Inist-CNRS/ezs/commit/326c6a0d7470703f339436da2d67cf08a50fa6db))





## [1.23.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.1...@ezs/core@1.23.2) (2021-04-07)


### Bug Fixes

* 🐛 security patch ([37b826b](https://github.com/Inist-CNRS/ezs/commit/37b826bf8481b5fa92e00c43420037df6edebba6))





## [1.23.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.23.0...@ezs/core@1.23.1) (2021-04-02)


### Bug Fixes

* 🐛 chdir for cluster ([8850615](https://github.com/Inist-CNRS/ezs/commit/88506151ebe94b75ac130feadb3e59db48f644f5))
* 🐛 restore previous behavior ([54bf0b8](https://github.com/Inist-CNRS/ezs/commit/54bf0b8d3f9795b21b64dce06178b65d3af4a3ae))





# [1.23.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.22.1...@ezs/core@1.23.0) (2021-04-02)


### Features

* 🎸 change current dir in daemon mode ([2646c1c](https://github.com/Inist-CNRS/ezs/commit/2646c1c6aa188672543bea319126d77f98018c65))





## [1.22.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.22.0...@ezs/core@1.22.1) (2021-04-02)


### Bug Fixes

* 🐛 compile doc & packages ([c276c1e](https://github.com/Inist-CNRS/ezs/commit/c276c1e113ba7f6f5c8f8e0f2ebfec9e3296941b))





# [1.22.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.21.2...@ezs/core@1.22.0) (2021-03-05)


### Features

* 🎸 add option noerror= to [URLFetch] ([2f6f768](https://github.com/Inist-CNRS/ezs/commit/2f6f768efd9bff8a75874ea399fb139f13a19a62))





## [1.21.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.21.1...@ezs/core@1.21.2) (2021-03-03)

**Note:** Version bump only for package @ezs/core





## [1.21.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.21.0...@ezs/core@1.21.1) (2021-01-29)

**Note:** Version bump only for package @ezs/core





# [1.21.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.20.0...@ezs/core@1.21.0) (2020-12-22)


### Features

* 🎸 remove useless method & statement ([d4000fb](https://github.com/Inist-CNRS/ezs/commit/d4000fb30f3720ed1c59019ab8c0d7cd1ceb6c4c))





# [1.20.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.19.0...@ezs/core@1.20.0) (2020-12-17)


### Bug Fixes

* 🐛 http compressionx ([a4edd96](https://github.com/Inist-CNRS/ezs/commit/a4edd96ea01696f9c787b93958411caaa22d25e4))
* print only string ([9332f94](https://github.com/Inist-CNRS/ezs/commit/9332f94ffe386d71ddf2f0d0049ed304a96b0e2e))


### Features

* 🎸 add server metrics mode ([f67efae](https://github.com/Inist-CNRS/ezs/commit/f67efae87476758f6f5415fcfa12a1de4a519ce7))
* add new statement [metrics] ([385cdb7](https://github.com/Inist-CNRS/ezs/commit/385cdb785d831e290aab2cc448261c22d864856b))
* add tracer mode ([3aaea2e](https://github.com/Inist-CNRS/ezs/commit/3aaea2ee0c76adee95561209189d2dadc864b0fa))





# [1.19.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.18.0...@ezs/core@1.19.0) (2020-10-23)


### Bug Fixes

* 🐛 [swing] & [remove] work the same way ([b0c759e](https://github.com/Inist-CNRS/ezs/commit/b0c759e953c196cc0461f597179421432f0f22c0))
* 🐛 handle leaked ([c343c5c](https://github.com/Inist-CNRS/ezs/commit/c343c5c7949b3d063f65803130066934f96ce6c6))


### Features

* 🎸 add [remove] statement ([037fd44](https://github.com/Inist-CNRS/ezs/commit/037fd44cafaaf59840ddcefccc9a90ebd329b157))
* 🎸 change title & description with env vars ([ec20fa5](https://github.com/Inist-CNRS/ezs/commit/ec20fa5a06c599865f07ba1de277b5ed556637b1))





# [1.18.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.17.1...@ezs/core@1.18.0) (2020-10-19)


### Features

* 🎸 add feed.flow()x ([a714656](https://github.com/Inist-CNRS/ezs/commit/a714656e6a328c40a6953cdd31559eb0623dde28))





## [1.17.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.17.0...@ezs/core@1.17.1) (2020-10-13)

**Note:** Version bump only for package @ezs/core





# [1.17.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.16.0...@ezs/core@1.17.0) (2020-10-05)


### Features

* 🎸 introduce [swing] ([a40ea5b](https://github.com/Inist-CNRS/ezs/commit/a40ea5bbfdb09319dad6f4433e677c549a865a79))





# [1.16.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.15.0...@ezs/core@1.16.0) (2020-09-28)


### Features

* 🎸 allow nested sections in .ini files ([1fddb41](https://github.com/Inist-CNRS/ezs/commit/1fddb41b3e5c8adb02f0d6a3a50972c4769d1312))





# [1.15.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.14.0...@ezs/core@1.15.0) (2020-09-17)


### Features

* 🎸 set cache delay with env var ([3d935d0](https://github.com/Inist-CNRS/ezs/commit/3d935d0d22fec73098457c16f5261b950d4c5732))





# [1.14.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.13.0...@ezs/core@1.14.0) (2020-09-16)


### Features

* 🎸 new statement [spawn] ([628a0bc](https://github.com/Inist-CNRS/ezs/commit/628a0bcf3a3b4ffcaea9862fc80e4c8e9171adab))





# [1.13.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.12.3...@ezs/core@1.13.0) (2020-09-14)


### Bug Fixes

* 🐛 avoid UnhandledPromiseRejectionWarning ([d1c3a58](https://github.com/Inist-CNRS/ezs/commit/d1c3a58f006290b980378d026eee0f091ec7fa07))
* error handler always close the response ([649e41b](https://github.com/Inist-CNRS/ezs/commit/649e41bf629df2f6465ade0c90a3c11d971ef2a2))
* prevent multiple errors emission ([745c639](https://github.com/Inist-CNRS/ezs/commit/745c639e3d7b1ef40b864ebee613a31b453d381a))


### Features

* add cumulative time for each statement ([0a79f61](https://github.com/Inist-CNRS/ezs/commit/0a79f61a2c65847f78e52c8a3a6ff32d8791e347))





## [1.12.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.12.2...@ezs/core@1.12.3) (2020-09-03)

**Note:** Version bump only for package @ezs/core





## [1.12.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.12.1...@ezs/core@1.12.2) (2020-08-28)

**Note:** Version bump only for package @ezs/core





## [1.12.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.12.0...@ezs/core@1.12.1) (2020-07-31)


### Bug Fixes

* 🐛 allow complex paths for openapi ([f94a4ad](https://github.com/Inist-CNRS/ezs/commit/f94a4adc4bc2bd004cc6c9fff11fa6a481dfb816))





# [1.12.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.11.0...@ezs/core@1.12.0) (2020-07-30)


### Bug Fixes

* in very rare cases, chunk doesn't exists ([ecd1f9e](https://github.com/Inist-CNRS/ezs/commit/ecd1f9eea573dee97743dd229bed36e6656cdbef))


### Features

* 🎸 server information switch to openapi ([e80b957](https://github.com/Inist-CNRS/ezs/commit/e80b95774b9ab3dcb371db70b777d03b82537bb8))





# [1.11.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.10.4...@ezs/core@1.11.0) (2020-07-29)


### Features

* 🎸 server sent 100 Continue to keep clients connected ([f79d9f3](https://github.com/Inist-CNRS/ezs/commit/f79d9f37a47120ea1160fcf33b70677ca70f4446))





## [1.10.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.10.3...@ezs/core@1.10.4) (2020-07-28)


### Bug Fixes

* 🐛 remove unused packages ([513d426](https://github.com/Inist-CNRS/ezs/commit/513d4267d362361dfd5ee439c185da4834aa0c5a))





## [1.10.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.10.2...@ezs/core@1.10.3) (2020-07-27)

**Note:** Version bump only for package @ezs/core





## [1.10.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.10.1...@ezs/core@1.10.2) (2020-07-27)

**Note:** Version bump only for package @ezs/core





## [1.10.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.10.0...@ezs/core@1.10.1) (2020-07-02)

**Note:** Version bump only for package @ezs/core





# [1.10.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.9.3...@ezs/core@1.10.0) (2020-06-26)


### Features

* 🎸 long lines can be cut with \ ([8fbfa0d](https://github.com/Inist-CNRS/ezs/commit/8fbfa0d25bc2a7bfd7a1dc1276af03740933dcbd))





## [1.9.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.9.2...@ezs/core@1.9.3) (2020-06-12)

**Note:** Version bump only for package @ezs/core





## [1.9.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.9.1...@ezs/core@1.9.2) (2020-05-12)


### Bug Fixes

* 🐛 useless condition ([04441f8](https://github.com/Inist-CNRS/ezs/commit/04441f88efc6e81e994d78527ee2e7e9ec1453aa))





## [1.9.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.9.0...@ezs/core@1.9.1) (2020-05-12)


### Bug Fixes

* 🐛 unable to disable cache from vars env ([1949927](https://github.com/Inist-CNRS/ezs/commit/1949927e65dd30a5ebf74139ceeb6138be4cdbcb))





# [1.9.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.8.3...@ezs/core@1.9.0) (2020-05-11)


### Bug Fixes

* 🐛 missing files ([049270b](https://github.com/Inist-CNRS/ezs/commit/049270b6073dee315a078f2e863e61d7000faa5b))
* avoid useless loop ([502f7a5](https://github.com/Inist-CNRS/ezs/commit/502f7a5fa28d767b858a969b890bbd63e77c48c0))


### Features

* 🎸 improve : mode / cache / cli ([8ee1993](https://github.com/Inist-CNRS/ezs/commit/8ee1993724d71b0c0fe1fae9b3929a7dcb1693c5))





## [1.8.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.8.2...@ezs/core@1.8.3) (2020-04-27)


### Bug Fixes

* 🐛 avoid confusion with ezs_concurrency ([0b60acd](https://github.com/Inist-CNRS/ezs/commit/0b60acd12343d5bd51e74e9548adfbfd202cba63))





## [1.8.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.8.1...@ezs/core@1.8.2) (2020-04-17)

**Note:** Version bump only for package @ezs/core





## [1.8.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.8.0...@ezs/core@1.8.1) (2020-04-06)


### Bug Fixes

* 🐛 allow 2 modesx ([d604b20](https://github.com/Inist-CNRS/ezs/commit/d604b209e6c10338eb49adc6767a4b4b88737476))
* 🐛 security patch bis ([0d7fa53](https://github.com/Inist-CNRS/ezs/commit/0d7fa5303ab68ea12be77b77fd21fbb4c4fbc943))





# [1.8.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.7.2...@ezs/core@1.8.0) (2020-04-02)


### Features

* 🎸 choose delegate mode with env vars ([526bb42](https://github.com/Inist-CNRS/ezs/commit/526bb42e237f1ac62dc74821cc737320a89e9056))





## [1.7.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.7.1...@ezs/core@1.7.2) (2020-03-31)


### Bug Fixes

* 🐛 extract cannot throw undefined or null values ([4bdfb94](https://github.com/Inist-CNRS/ezs/commit/4bdfb9404b5c73bba1f0012104f0f71bec1dcc3a))





## [1.7.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.7.0...@ezs/core@1.7.1) (2020-03-30)


### Bug Fixes

* 🐛 'Invalid Argument' error ([debbb07](https://github.com/Inist-CNRS/ezs/commit/debbb07f6b074cff01c5385206e92c797e2d69c6))
* 🐛 ncu ([a05dcee](https://github.com/Inist-CNRS/ezs/commit/a05dcee3a8832a677706b8d0b30370f075785639))





# [1.7.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.6.4...@ezs/core@1.7.0) (2020-03-23)


### Features

* 🎸 add statement [aggregate] ([399224d](https://github.com/Inist-CNRS/ezs/commit/399224d50550c67da0ac249cc5df88f759744329))





## [1.6.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.6.3...@ezs/core@1.6.4) (2020-03-02)


### Performance Improvements

* ⚡️ server can delegate or dispatch statements ([3a74d47](https://github.com/Inist-CNRS/ezs/commit/3a74d478b279809444c8ceb23999dabb17a5a3ce))
* ⚡️ x100 ([b7a98cf](https://github.com/Inist-CNRS/ezs/commit/b7a98cf0f116e7374c0fd1e03ad0fe573b01d989))





## [1.6.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.6.2...@ezs/core@1.6.3) (2020-02-28)


### Bug Fixes

* 🐛 small fix to avoid double ending in some cases ([2024384](https://github.com/Inist-CNRS/ezs/commit/202438411109728cb2ec7a4f2b8883150f6ea79d))





## [1.6.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.6.1...@ezs/core@1.6.2) (2020-02-27)

**Note:** Version bump only for package @ezs/core





## [1.6.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.6.0...@ezs/core@1.6.1) (2020-02-26)

**Note:** Version bump only for package @ezs/core





# [1.6.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.5.1...@ezs/core@1.6.0) (2020-02-25)


### Features

* 🎸 new statement [exchange] ([848f174](https://github.com/Inist-CNRS/ezs/commit/848f17440d9c655d6f2284b1c6d65a670c0feeea))





## [1.5.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.5.0...@ezs/core@1.5.1) (2020-02-03)


### Bug Fixes

* 🐛 dirname was lost ([9bddefd](https://github.com/Inist-CNRS/ezs/commit/9bddefdce55743ad0ca8dd0d5fcee9fc41235d2a))





# [1.5.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.3.1...@ezs/core@1.5.0) (2020-01-10)


### Features

* **core:** new statement [ignore] ([0d818b6](https://github.com/Inist-CNRS/ezs/commit/0d818b66caff74e66345cecda739af75e6c0ef7a))





# [1.4.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.3.1...@ezs/core@1.4.0) (2020-01-10)


### Features

* **core:** new statement [ignore] ([0d818b6](https://github.com/Inist-CNRS/ezs/commit/0d818b66caff74e66345cecda739af75e6c0ef7a))





## [1.3.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.3.0...@ezs/core@1.3.1) (2019-12-20)


### Bug Fixes

* **core:** 🐛 missing package with Travis ([93e2414](https://github.com/Inist-CNRS/ezs/commit/93e24148a7f921852dda1d2ca88a2db05dc55999))





# [1.3.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.2.4...@ezs/core@1.3.0) (2019-12-13)


### Features

* 🎸 special server route for identifiers ([07e06de](https://github.com/Inist-CNRS/ezs/commit/07e06de4193cf3a4e3c53f0f7c464f4e444bd5bc))





## [1.2.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.2.3...@ezs/core@1.2.4) (2019-11-28)

**Note:** Version bump only for package @ezs/core





## [1.2.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.2.2...@ezs/core@1.2.3) (2019-11-18)

**Note:** Version bump only for package @ezs/core





## [1.2.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.2.1...@ezs/core@1.2.2) (2019-11-07)

**Note:** Version bump only for package @ezs/core





## [1.2.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.2.0...@ezs/core@1.2.1) (2019-11-02)

**Note:** Version bump only for package @ezs/core





# [1.2.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.7...@ezs/core@1.2.0) (2019-10-25)


### Bug Fixes

* 🐛 find statement with prefix ([6f74b33](https://github.com/Inist-CNRS/ezs/commit/6f74b33)), closes [#15](https://github.com/Inist-CNRS/ezs/issues/15)


### Features

* **core:** Add naive version of time statement ([8bede60](https://github.com/Inist-CNRS/ezs/commit/8bede60))





## [1.1.7](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.6...@ezs/core@1.1.7) (2019-09-28)

**Note:** Version bump only for package @ezs/core





## [1.1.6](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.5...@ezs/core@1.1.6) (2019-09-13)

**Note:** Version bump only for package @ezs/core





## [1.1.5](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.4...@ezs/core@1.1.5) (2019-09-13)

**Note:** Version bump only for package @ezs/core





## [1.1.4](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.3...@ezs/core@1.1.4) (2019-09-11)

**Note:** Version bump only for package @ezs/core





## [1.1.3](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.2...@ezs/core@1.1.3) (2019-09-09)

**Note:** Version bump only for package @ezs/core





## [1.1.2](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.1...@ezs/core@1.1.2) (2019-09-06)

**Note:** Version bump only for package @ezs/core





## [1.1.1](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.1.0...@ezs/core@1.1.1) (2019-09-06)

**Note:** Version bump only for package @ezs/core





# [1.1.0](https://github.com/Inist-CNRS/ezs/compare/@ezs/core@1.0.0...@ezs/core@1.1.0) (2019-09-06)


### Features

* **core:** Allow the use of an [@ezs](https://github.com/ezs) package ([b5df9d0](https://github.com/Inist-CNRS/ezs/commit/b5df9d0))





# 1.0.0 (2019-09-05)


### Bug Fixes

* **core:** Fix statement get() ([236ef7e](https://github.com/Inist-CNRS/ezs/commit/236ef7e))
* **core:** Update cli's help ([acd77ee](https://github.com/Inist-CNRS/ezs/commit/acd77ee))
