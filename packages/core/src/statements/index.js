import extract from './extract.js';
import assign from './assign.js';
import replace from './replace.js';
import shift from './shift.js';
import pop from './pop.js';
import keep from './keep.js';
import debug from './debug.js';
import concat from './concat.js';
import remove from './remove.js';
import unpack from './unpack.js';
import pack from './pack.js';
import transit from './transit.js';
import shuffle from './shuffle.js';
import env from './env.js';
import group from './group.js';
import ungroup from './ungroup.js';
import parallel from './parallel.js';
import spawn from './spawn.js';
import delegate from './delegate.js';
import singleton from './singleton.js';
import tracer from './tracer.js';
import truncate from './truncate.js';
import validate from './validate.js';
import dump from './dump.js';
import time from './time.js';
import ignore from './ignore.js';
import exchange from './exchange.js';
import swing from './swing.js';
import loop from './loop.js';
import map from './map.js';
import dedupe from './dedupe.js';
import identify from './identify.js';
import throttle from './throttle.js';
import combine from './combine.js';
import expand from './expand.js';
import overturn from './overturn.js';
import fork from './fork.js';
import breaker from './breaker.js';
import detach from './detach.js';

export default {
    extract,
    assign,
    replace,
    shift,
    pop,
    keep,
    debug,
    concat,
    remove,
    unpack,
    pack,
    transit,
    shuffle,
    env,
    group,
    ungroup,
    parallel,
    tracer,
    spawn,
    delegate,
    singleton,
    truncate,
    validate,
    dump,
    time,
    ignore,
    exchange,
    swing,
    loop,
    map,
    dedupe,
    identify,
    throttle,
    expand,
    combine,
    overturn,
    fork,
    breaker,
    detach,
};
