/* eslint no-underscore-dangle: ["error", { "allow": ["_readableState"] }] */
import debug from 'debug';
import { types } from 'util';
import queue from 'concurrent-queue';
import { hrtime } from 'process';
import eos from 'end-of-stream';
import pWaitFor from 'p-wait-for';
import stringify from 'json-stringify-safe';
import Feed from './feed.js';
import Shell from './shell.js';

import SafeTransform from './SafeTransform.js';

/**
 * Engine scope object type
 * @private
 * @typedef {Object} EngineScope
 *
 * @property {Engine} ezs
 * @property {(d: unknown, c: unknown) => void} emit
 * @property {() => any} getParams
 * @property {() => boolean} isFirst
 * @property {() => number} getIndex
 * @property {() => boolean} isLast
 * @property {() => string} getCumulativeTime
 * @property {() => number} getCumulativeTimeMS
 * @property {() => number} getCounter
 * @property {(name: string, defval?: string | string[], chunk?: unknown) => string | string[] | undefined} getParam
 */

const nanoZero = () => BigInt(0);

const nano2sec = (ns) => {
    const sec = ns / BigInt(1e9);
    const msec = (ns / BigInt(1e6)) - (sec * BigInt(1e3));
    const time = Number(sec) + (Number(msec) / 10000);
    return Number(time).toFixed(4);
};
let counter = 0;
function increaseCounter() {
    counter += 1;
}
function decreaseCounter() {
    counter -= 1;
}

function createErrorWith(error, index, funcName, funcParams, chunk) {
    const stk = String(error.stack).split('\n');
    const prefix = `item #${index} `;
    const erm = stk.shift().replace(prefix, '');
    const msg = `${prefix}[${funcName}] <${erm}>\n\t${stk.slice(0, 10).join('\n\t')}`;
    const err = Error(msg);
    err.sourceError = error;
    err.sourceChunk = Buffer.isBuffer(chunk) ? 'Buffer' : stringify(chunk);
    err.type = error.type || 'Standard error';
    err.scope = error.scope || 'code';
    err.date = error.date || new Date();
    err.message = msg.split('\n').shift();
    err.func = funcName;
    err.params = funcParams;
    err.traceback = stk.slice(0,10);
    err.index = index;
    Error.captureStackTrace(err, createErrorWith);
    return err;
}

export default class Engine extends SafeTransform {
    constructor(ezs, func, params, environment) {
        super(ezs.objectMode());
        this.funcName = 'Not yet defined';
        this.funcPromise = types.isPromise(func) ? func : Promise.resolve(func);
        this.index = 0;
        this.ttime = nanoZero();
        this.stime = hrtime.bigint();
        this.params = params || {};
        this.ezs = ezs;
        this.environment = environment || {};
        this.errorWasSent = false;
        this.nullWasSent = false;
        this.queue = queue().limit(ezs.settings.queue).process((task, cb) => {
            this.execWith(task, cb);
        });
        this.on('pipe', (src) => {
            this.parentStream = src;
        });
        increaseCounter();
        eos(this, decreaseCounter);
        this.shell = new Shell(ezs, this.environment);
        this.chunk = {};
        /**
         * @private
         * @type {EngineScope}
         */
        this.scope = {};
        this.scope.getEnv = (name) => (name === undefined ? this.environment : this.environment[name]);
        this.scope.ezs = this.ezs;
        this.scope.emit = (d, c) => this.emit(d, c);
        this.scope.getParams = () => this.params;
        this.scope.isFirst = () => (this.index === 1);
        this.scope.getIndex = () => this.index;
        this.scope.isLast = () => (this.chunk === null);
        this.scope.getCumulativeTime = () => nano2sec(hrtime.bigint() - this.stime);
        this.scope.getCumulativeTimeMS = () => nano2sec(hrtime.bigint() - this.stime) * 1000;
        this.scope.getCounter = () => counter;
        this.scope.getParam = (name, defval, chunk) => {
            if (this.params[name] !== undefined) {
                return this.shell.run(this.params[name], chunk || this.chunk);
            }
            return defval;
        };
    }

    _transform(chunk, encoding, done) {
        const start = hrtime.bigint();
        const next = () => {
            if (debug.enabled('ezs:trace')) {
                this.ttime += (hrtime.bigint() - start);
            }
            done();
        };
        if (this.nullWasSent) {
            if (this.parentStream && this.parentStream.unpipe) {
                this.parentStream.unpipe(this);
            }
            return next();
        }
        this.index += 1;
        if (chunk instanceof Error) {
            this.push(chunk);
            return next();
        }
        this.queuing(chunk, next);
    }

    _flush(done) {
        if (this.nullWasSent) {
            return done();
        }
        this.index += 1;
        return this.queuing(null, () => {
            const stop = hrtime.bigint();
            if (debug.enabled('ezs:trace')) {
                const cumulative = nano2sec(stop - this.stime);
                const elapsed = nano2sec(this.ttime);
                debug('ezs:trace')(`${cumulative}s cumulative ${elapsed}s elapsed for [${this.funcName}]`);
            }
            done();
        });
    }

    queuing(chunk, next) {
        if (this.func) {
            return this.queue(chunk, next);
        } else {
            this.funcPromise
                .then((func) => {
                    if (typeof func != 'function'){
                        this.emit('error', new Error(
                            `'  ${func}' is not loaded. It's not a valid statement function.`,
                        ));
                        return next();
                    }
                    this.func = func;
                    this.funcName = String(func.name || 'unamed');
                    return this.queue(chunk, next);
                })
                .catch( (e) => {
                    this.emit('error', e);
                    return next();
                });
        }
    }

    isReady() {
        return ((!this._readableState.ended
            && (this._readableState.length < this._readableState.highWaterMark
                || this._readableState.length === 0))
            || this.nullWasSent);
    }

    execWith(chunk, done) {
        if (this.errorWasSent || this.nullWasSent) {
            return done();
        }
        const currentIndex = this.index;
        if (chunk === null && currentIndex === 1) {
            this.nullWasSent = true;
            this.push(null);
            return done();
        }
        const warn = (error) => {
            if (!this.errorWasSent) {
              this.errorWasSent = true;
              const warnErr = createErrorWith(error, currentIndex, this.funcName, this.params, chunk);
              debug('ezs:warn')('ezs engine emit an', this.ezs.serializeError(warnErr));
              this.emit('error', warnErr);
            }
        };
        const push = (data) => {
            if (data === null) {
                this.nullWasSent = true;
                this.nullWasSentError = createErrorWith(new Error('As a reminder, the end was recorded at this point'), currentIndex, this.funcName, this.params, chunk);
            } else if (this.nullWasSent && !this.errorWasSent) {
                debug('ezs:warn')('Unstable state', this.ezs.serializeError(createErrorWith(new Error('Oops, that\'s going to crash ?'), currentIndex, this.funcName, this.params, chunk)));
                return warn(this.nullWasSentError);
            }
            if (!this.nullWasSent && this._readableState.ended) {
                return warn(new Error('No back pressure control ?'));
            }
            if (data instanceof Error) {
                const ignoreErr = createErrorWith(data, currentIndex, this.funcName, this.params, chunk);
                debug('ezs:info')(`Ignoring error at item #${currentIndex}`, this.ezs.serializeError(ignoreErr));
                return this.push(ignoreErr);
            }
            if (!this.errorWasSent) {
                return this.push(data);
            }
        };
        const wait = async () => {
            this.pause();
            await pWaitFor(() => (this.isReady()), { interval: 20 });
            return this.resume();
        };
        const feed = new Feed(this.ezs, push, done, warn, wait);
        feed.engine = this;
        try {
            this.chunk = chunk;
            return Promise.resolve(this.func.call(this.scope, chunk, feed, this.scope)).catch((e) => {
                const asyncErr = createErrorWith(e, currentIndex, this.funcName, this.params, chunk);
                debug('ezs:error')(`Async error thrown at item #${currentIndex}, pipeline is broken`, this.ezs.serializeError(asyncErr));
                this.emit('error', asyncErr);
                done();
            });
        } catch (e) {
            const syncErr = createErrorWith(e, currentIndex, this.funcName, this.params, chunk);
            debug('ezs:error')(`Sync error thrown at item #${currentIndex}, pipeline carries errors`, this.ezs.serializeError(syncErr));
            this.push(syncErr);
            return done();
        }
    }
}
