[use]
plugin = basics
plugin = analytics

[XMLParse]
separator = /gexf/graph/attributes
separator = /gexf/graph/nodes/node
separator = /gexf/graph/edges/edge
[swing]
test = get("attribute").isUndefined()
reverse = true
[swing/env]
path = attributes
value = get('attribute').castArray().map(x => x.title)


[debug]
[swing]
test = get("source").isUndefined()
reverse = true
[swing/replace]
path = id
value = get('source')
path = targets.0.id
value = get('target')
path = targets.0.value
value = get('value')

[replace]
path = id
value = get('id')
path = value
value = self().omit('id')

[aggregate]
path = id

[replace]
path = id
value = get('id')
path = value
value = get('value').thru(arrObj => _.mergeWith({}, ...arrObj, (objValue, srcValue) => _.isArray(objValue) ? objValue.concat(srcValue) : undefined ))

[assign]
path = value
value = get('value').omit('attvalues')
path = attributes
value = get('value.attvalues.attvalue').castArray().map(x=>({[env(`attributes.${x.for}`)]: x.value})).thru(x => _.merge({}, ...x))
[debug]

[dump]
indent = true
