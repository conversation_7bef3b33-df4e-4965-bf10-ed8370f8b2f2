[use]
plugin = basics
plugin = teeftfr
plugin = analytics

[JSONParse]
separator = hits.*

[replace]
path = content
value = get('abstract')

path = path
value = get('arkIstex')

[drop]
path = content

[ToLowerCase]
path = content

[TEEFTSentenceTokenize]
[TEEFTTokenize]
[TEEFTNaturalTag]
[TEEFTExtractTerms]
nounTag = NOM
adjTag = ADJ
[TEEFTFilterTags]
tags = NOM
tags = ADJ
tags = UNK
[TEEFTStopWords]
[TEEFTSumUpFrequencies]
[TEEFTSpecificity]
sort = true
[TEEFTFilterMonoFreq]

[replace]
path = id
value = get('0.path')
path = value
value = get('0.terms').map('term').slice(0, _.env(null, 'limit', 7))

[CSVString]
header = false
separator = fix('\t')
