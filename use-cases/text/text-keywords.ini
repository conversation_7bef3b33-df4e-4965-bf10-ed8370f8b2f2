[use]
plugin = basics
plugin = teeftfr
plugin = analytics

[concat]

[replace]
path = content
value = self().trim()

path = path
value = env('id', 'id')

[ToLowerCase]
path = content
[TEEFTSentenceTokenize]
[TEEFTTokenize]
[TEEFTNaturalTag]
[TEEFTExtractTerms]
nounTag = NOM
adjTag = ADJ
[TEEFTFilterTags]
tags = NOM
tags = ADJ
tags = UNK
[TEEFTStopWords]
[TEEFTSumUpFrequencies]
[TEEFTSpecificity]
sort = true
[TEEFTFilterMonoFreq]

[exploding]
id = 0.path
value = 0.terms

#[debug]
[slice]
size = env('size', 10)

[replace]
path = term
value = get('value.term')

[CSVString]
header = false
