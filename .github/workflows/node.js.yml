# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node-version: [14.x, 16.x, 18.x, 20.x, 22.x, 24.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v3
      - name: Install libpostal
        run: bash ${GITHUB_WORKSPACE}/.github/workflows/install-libpostal.sh
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"

      - name: Install dependencies (Node.js >= 22)
        if: ${{ matrix.node-version == '22.x' || matrix.node-version == '24.x' }}
        env:
          CXXFLAGS: "-I/deps/include"
          LDFLAGS: "-L/deps/lib"
        run: yarn run ci:install

      - name: Install dependencies (Node.js < 22) - Skip @ezs/storage
        if: ${{ matrix.node-version != '22.x' && matrix.node-version != '24.x' }}
        env:
          CXXFLAGS: "-I/deps/include"
          LDFLAGS: "-L/deps/lib"
        run: |
          # Temporarily rename storage package to exclude it from workspaces glob
          mv packages/storage packages/storage-disabled
          yarn install --ignore-engines --frozen-lockfile
          # Build all packages except storage
          yarn workspaces run build
          # Restore storage package name
          mv packages/storage-disabled packages/storage

      - name: Rebuild node-postal (Mainly use to NodeJS 20)
        env:
          CXXFLAGS: "-I/deps/include"
          LDFLAGS: "-L/deps/lib"
        run: |
          cd node_modules/node-postal
          yarn run install

      - name: Testing all packages (Node.js >= 22)
        if: ${{ matrix.node-version == '22.x' || matrix.node-version == '24.x' }}
        env:
          LD_LIBRARY_PATH: "/deps/lib"
          DEBUG: "ezs:error,ezs:warn"
        run: yarn run ci:test

      - name: Testing packages except storage (Node.js < 22)
        if: ${{ matrix.node-version != '22.x' && matrix.node-version != '24.x' }}
        env:
          LD_LIBRARY_PATH: "/deps/lib"
          DEBUG: "ezs:error,ezs:warn"
        run: |
          # Run tests for all packages except storage
          NODE_ENV=test npx jest --maxWorkers 4 --verbose --testPathIgnorePatterns="packages/storage"

      - name: Coveralls Parallel
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          flag-name: run-${{ matrix.node-version }}
          parallel: true

  finish:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Coveralls Finished
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.github_token }}
          parallel-finished: true
