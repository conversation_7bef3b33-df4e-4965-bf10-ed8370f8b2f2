{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Suivi des flux (ezs) dans les workers ", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 7, "iteration": 1666930524757, "links": [], "liveNow": false, "panels": [{"description": "irate of stream counter", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 19, "x": 0, "y": 0}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "irate(ezs_stream_total{instance=~\"$instance\", pathName=~\"$pathName\"}[5m])", "interval": "", "legendFormat": "{{instance}}{{pathName}}", "refId": "A"}], "title": "Usage counter", "type": "timeseries"}, {"description": "since the last restart of the instance", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 5, "x": 19, "y": 0}, "id": 6, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["range"], "fields": "", "limit": 1, "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "sum(ezs_stream_total{instance=~\"$instance\",pathName=~\"$pathName\"})\n", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Number of requests", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 5, "x": 19, "y": 3}, "id": 14, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["range"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "sum(ezs_stream_chunks_count{instance=~\"$instance\",pathName=~\"$pathName\"}) ", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Number of chunks", "type": "stat"}, {"description": "irate of stream duration", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 19, "x": 0, "y": 6}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "irate(ezs_stream_duration_ms_count{instance=~\"$instance\", pathName=~\"$pathName\"}[$__interval])", "interval": "", "legendFormat": "{{job}}{{pathName}}", "refId": "A"}], "title": "Usage Duration ", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 19, "y": 6}, "id": 22, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": false}, "pluginVersion": "8.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "sum(http_connection_open)", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Connections", "type": "gauge"}, {"description": "of each entrypoint", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Input / Output", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 19, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "irate(ezs_stream_chunks_count{instance=~\"$instance\", pathName=~\"$pathName\",bucket=\"output\"}[2m])", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{job}}{{pathName}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": true, "expr": "-1 * irate(ezs_stream_chunks_count{instance=~\"$instance\", pathName=~\"$pathName\",bucket=\"input\"}[2m])", "hide": false, "interval": "", "legendFormat": "{{job}}{{pathName}}", "refId": "B"}], "title": "Usage I/O", "transparent": true, "type": "timeseries"}, {"description": "is the number of distinct containers", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 5, "x": 19, "y": 12}, "id": 20, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "textMode": "auto"}, "pluginVersion": "8.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": false, "expr": "count(count(ezs_stream_total) by (instance))", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Number of instances", "type": "stat"}, {"description": "is the number of distinct pathname", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 5, "x": 19, "y": 15}, "id": 18, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "textMode": "auto"}, "pluginVersion": "8.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": false, "expr": "count(count(ezs_stream_total) by (pathName))", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Number of entry points", "type": "stat"}, {"cards": {}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "description": "Stream buckets' distribution ", "gridPos": {"h": 6, "w": 19, "x": 0, "y": 18}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 26, "legend": {"show": false}, "maxDataPoints": 25, "pluginVersion": "8.4.2", "reverseYBuckets": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": false, "expr": "sum(increase(ezs_stream_size_bytes_bucket{instance=~\"$instance\", pathName=~\"$pathName\",bucket=\"input\"}[$__interval])) by (le) ", "format": "heatmap", "instant": false, "interval": "", "legendFormat": "{{le}}", "refId": "A"}], "title": "Bytes recevied ", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "yAxis": {"decimals": 2, "format": "bytes", "logBase": 1, "show": true}, "yBucketBound": "upper"}, {"description": "distinct by nodejs version", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 19, "y": 18}, "id": 24, "options": {"barRadius": 0, "barWidth": 0.71, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "orientation": "horizontal", "showValue": "auto", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xField": "version", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": false, "expr": "count(count(nodejs_version_info) by (job, version)) by (version)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Workers version Usage", "type": "barchart"}, {"cards": {}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolate<PERSON><PERSON>s", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "gridPos": {"h": 6, "w": 19, "x": 0, "y": 24}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 28, "legend": {"show": false}, "maxDataPoints": 25, "reverseYBuckets": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Q2P1W1Pnz"}, "exemplar": false, "expr": "sum(increase(ezs_stream_chunks_bucket{instance=~\"$instance\", pathName=~\"$pathName\",bucket=\"input\"}[$__interval])) by (le) ", "format": "heatmap", "interval": "", "legendFormat": "{{le}}", "refId": "A"}], "title": "Objects received", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "show": true}, "yBucketBound": "auto"}, {"gridPos": {"h": 6, "w": 9, "x": 0, "y": 30}, "id": 12, "options": {"content": "Le nombre de requêtes et de chunks reçus par la machine. \n - *TOFIX* : indiquer depuis quand \n - *TODO* : mettre en corrélation avec les requêtes en erreur\n - *TODO* : montrer l'évolution du nombre de requêtes pour montrer les pics de connexion\n\n Mettre en évidence le temps de réponse par requête.\n - *TODO* : corr<PERSON>ler le temps de réponse et le nombre de documents traités\n - *TODO* : proposer un histogramme ", "mode": "markdown"}, "pluginVersion": "8.4.2", "title": "TODO", "type": "text"}], "refresh": "1m", "schemaVersion": 35, "style": "dark", "tags": ["production"], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(nodejs_version_info, instance)", "hide": 0, "includeAll": true, "label": "instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(nodejs_version_info, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(ezs_statement_chunks_total, pathName)", "description": "Path of the entrypoint ", "hide": 0, "includeAll": true, "label": "pathName", "multi": true, "name": "pathName", "options": [], "query": {"query": "label_values(ezs_statement_chunks_total, pathName)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "EZS Overview (vptdmservices)", "uid": "snrE51Enz", "version": 32, "weekStart": ""}