# ezs

[![lerna](https://img.shields.io/badge/maintained%20with-lerna-cc00ff.svg)](https://lerna.js.org/)
[![GitHub Action Status](https://github.com/Inist-CNRS/ezs/actions/workflows/node.js.yml/badge.svg)](./.github/workflows/node.js.yml)
[![Coverage Status](https://coveralls.io/repos/github/Inist-CNRS/ezs/badge.svg?branch=master)](https://coveralls.io/github/Inist-CNRS/ezs?branch=master)

Repository containing all @ezs packages (monorepo)

The standard package is [@ezs/core](./packages/core#readme), which contains its binary.

[@ezs/core](./packages/core#readme) [![npm version](https://img.shields.io/npm/v/@ezs/core)](https://npm.im/@ezs/core)

## Packages

- [@ezs/analytics](./packages/analytics#readme) [![npm version](https://img.shields.io/npm/v/@ezs/analytics)](https://npm.im/@ezs/analytics)
- [@ezs/basics](./packages/basics#readme) [![npm version](https://img.shields.io/npm/v/@ezs/basics)](https://npm.im/@ezs/basics)
- [@ezs/booster](./packages/booster#readme) [![npm version](https://img.shields.io/npm/v/@ezs/booster)](https://npm.im/@ezs/booster)
- [@ezs/conditor](./packages/conditor#readme) [![npm version](https://img.shields.io/npm/v/@ezs/conditor)](https://npm.im/@ezs/conditor)
- [@ezs/istex](./packages/istex#readme) [![npm version](https://img.shields.io/npm/v/@ezs/istex)](https://npm.im/@ezs/istex)
- [@ezs/libpostal](./packages/libpostal#readme) [![npm version](https://img.shields.io/npm/v/@ezs/libpostal)](https://npm.im/@ezs/libpostal)
- [@ezs/loterre](./packages/loterre#readme) [![npm version](https://img.shields.io/npm/v/@ezs/loterre)](https://npm.im/@ezs/loterre)
- [@ezs/sparql](./packages/sparql#readme) [![npm version](https://img.shields.io/npm/v/@ezs/sparql)](https://npm.im/@ezs/sparql)
- [@ezs/spawn](./packages/spawn#readme) [![npm version](https://img.shields.io/npm/v/@ezs/spawn)](https://npm.im/@ezs/spawn)
- [@ezs/storage](./packages/storage#readme) [![npm version](https://img.shields.io/npm/v/@ezs/storage)](https://npm.im/@ezs/storage)
- [@ezs/strings](./packages/strings#readme) [![npm version](https://img.shields.io/npm/v/@ezs/strings)](https://npm.im/@ezs/strings)
- [@ezs/teeft](./packages/teeft#readme) [![npm version](https://img.shields.io/npm/v/@ezs/teeft)](https://npm.im/@ezs/teeft)
- [@ezs/transformers](./packages/transformers#readme) [![npm version](https://img.shields.io/npm/v/@ezs/transformers)](https://npm.im/@ezs/transformers)
- [@ezs/xslt](./packages/xslt#readme) [![npm version](https://img.shields.io/npm/v/@ezs/xslt)](https://npm.im/@ezs/xslt)

## History

This [repository](https://github.com/Inist-CNRS/ezs) is a grouping of several packages.
They were named `ezs-*`, and are now named `@ezs/*`  (except `ezs` which is now named `@ezs/core`).

`@ezs/core@1.0.0` comes from `ezs@9.4.2`.
