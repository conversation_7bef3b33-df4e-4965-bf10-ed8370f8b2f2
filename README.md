# Formation EZS

Cette formation de 2 demi-journées de 3 heures est prévue pour être donnée en
interne à l'Inist-CNRS.

1. [Introduction](Introduction.md)
   1. [Quoi ?](Introduction.md#quoi-)
      1. Javascript / node
      2. données structurées / flux
   2. [Où ?](Introduction.md#o%C3%B9-)
      1. lodex (enrichissements, loaders, routines, exporters)
      2. services web TDM
      3. web dumps
   3. [Comment ?](Introduction.md#comment-)
      1. shell (`.ini`, `npx`)
      2. javascript
      3. playground
2. [Instructions d'entrée-sortie](basics.md)
   1. [JSON (parse / dump)](basics.md#json-parse--dump)
   2. [JSONL (unpack / pack)](basics.md#jsonl-unpack--pack)
   3. [CSV (parse / string)](basics.md#csv-parse--string)
   4. [URL (connect)](basics.md#url-connect)
   5. [Exercices: json2jsonl, jsonl2tsv, ...](basics.md#exercices-json2jsonl-jsonl2tsv-et-caetera)
3. [Instructions essentielles](core.md)
   1. [assign](core.md#assign)
   2. [expand](core.md#expand)
   3. [env](core.md#env)
   4. [singleton](core.md#singleton)
   5. [throttle](core.md#throttle)
   6. [dedupe](core.md#dedupe)
   7. [Exercices: quadruple, départements, id, attendre, ...](core.md#exercices)
4. [Usages avancés](advanced.md)
   1. [plugins (analytics, teeft, istex, conditor, sparql, spawn, ...)](advanced.md#plugins)
   2. [scripts complexes](advanced.md#scripts-complexes)
   3. [loaders](advanced.md#loaders)
   4. [le cas Lodash](advanced.md#le-cas-lodash)
5. [Ressources](resources.md)
   1. [playground](resources.md#playground)
   2. [npm](resources.md#npm)
   3. [documentation](resources.md#documentation)
   4. [parcours complémentaire Lodash](resources.md#parcours-complémentaire-lodash)
   5. [github (ezs, lodex/ezsLodex)](resources.md#github-ezs-lodexezslodex)
