# istex

Ce plugin propose une série d'instructions liées à l’usage de l’API [ISTEX](http://www.istex.fr/)

## installation

```bash
npm install @ezs/istex
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [ISTEX](#istex)
*   [ISTEXFacet](#istexfacet)
*   [ISTEXFetch](#istexfetch)
*   [ISTEXFiles](#istexfiles)
*   [ISTEXFilesContent](#istexfilescontent)
*   [ISTEXFilesWrap](#istexfileswrap)
*   [ISTEXParseDotCorpus](#istexparsedotcorpus)
*   [ISTEXResult](#istexresult)
*   [ISTEXSave](#istexsave)
*   [ISTEXScroll](#istexscroll)
*   [ISTEXTriplify](#istextriplify)
*   [ISTEXUniq](#istexuniq)
*   [ISTEXUnzip](#istexunzip)

### ISTEX

Take an array and returns matching documents for every value of the array

#### Parameters

*   `query` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) | [Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>)** ISTEX query (or queries) (optional, default `data.query||[]`)
*   `id` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) | [Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>)** ISTEX id (or ids) (optional, default `data.id||[]`)
*   `maxPage` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** maximum number of pages to get
*   `size` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** size of each page of results
*   `duration` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** maximum duration between two requests (ex: "30s")
*   `field` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>** fields to output

#### Examples

```javascript
.pipe(ezs('ISTEX', {
  query: 'this is a test',
  size: 3,
  maxPage: 1,
  sid: 'test'
}))
```

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>**&#x20;

### ISTEXFacet

Take an object containing a query string field, a facet, and output
aggregations from the ISTEX API.

#### Parameters

*   `query` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** ISTEX query (optional, default `"*"`)
*   `facet` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** ISTEX facet (optional, default `"corpusName"`)
*   `sid` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** User-agent identifier (optional, default `"ezs-istex"`)

#### Examples

```javascript
from([{ query: 'ezs', facet: 'corpusName' }])
  .pipe(ezs('ISTEXFacet', { sid: 'test', }))
```

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>**&#x20;

### ISTEXFetch

Take `Object` with `id` and returns the document's metadata

#### Parameters

*   `source` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Field to use to fetch documents (optional, default `"id"`)
*   `target` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;
*   `id` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** ISTEX Identifier of a document (optional, default `data.id`)
*   `sid` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** User-agent identifier (optional, default `"ezs-istex"`)

#### Examples

Input:

```javascript
[{
  id: '87699D0C20258C18259DED2A5E63B9A50F3B3363',
}, {
  id: 'ark:/67375/QHD-T00H6VNF-0',
}]
```

will produce two JSON records.

```javascript
.pipe(ezs('ISTEXFetch', { source: 'id' }))
```

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>**&#x20;

### ISTEXFiles

Take an Object with ISTEX `id` and generate an object for each file

See [ISTEXScroll](#istexscroll)

#### Parameters

*   `fulltext` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** typology of the document to save (optional, default `pdf`)
*   `metadata` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** format of the files to save (optional, default `json`)
*   `enrichment` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** enrichment of the document to save
*   `sid` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** User-agent identifier (optional, default `"ezs-istex"`)

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)**&#x20;

### ISTEXFilesContent

Take an Object with ISTEX `source` and check the document's file.
Warning: to access fulltext, you have to give a `token` parameter.
ISTEXFetch produces the stream you need to save the file.

See [ISTEXFiles](#istexfiles)

#### Parameters

*   `sid` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** User-agent identifier (optional, default `"ezs-istex"`)
*   `token` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** authentication token (see [documentation](https://doc.istex.fr/api/access/fede.html#acc%C3%A8s-programmatique-via-un-token-didentification))

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### ISTEXFilesWrap

Take and Object with ISTEX `stream` and wrap into a single zip

See [ISTEXFiles](#istexfiles)

Returns **[Buffer](https://nodejs.org/api/buffer.html)**&#x20;

### ISTEXParseDotCorpus

Parse a `.corpus` file content, and execute the action contained in the
`.corpus` file.

<caption>1query.corpus</caption>

```ini
[ISTEX]
query = language.raw:rum
field = doi
field = author
field = title
field = language
field = publicationDate
field = keywords
field = host
field = fulltext
```

<caption>1notice.corpus</caption>

```ini
[ISTEX]
id 2FF3F5B1477986B9C617BB75CA3333DBEE99EB05
```

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### ISTEXResult

Take `Object` containing results of ISTEX API, and returns `hits` value
(documents).

This should be placed after ISTEXScroll.

See [ISTEXScroll](#istexscroll)

#### Parameters

*   `source` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**  (optional, default `data`)
*   `target` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**  (optional, default `feed`)

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>**&#x20;

### ISTEXSave

Take and Object with ISTEX `id` and save the document's file.

Warning: to access fulltext, you have to give a `token` parameter.

[ISTEXFetch](#istexfetch) produces the stream you need to save the file.

See [ISTEXFetch](#istexfetch)

#### Parameters

*   `directory` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path for the PDFs (optional, default `currentworkingdirectory`)
*   `typology` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** typology of the document to save (optional, default `"fulltext"`)
*   `format` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** format of the files to save (optional, default `"pdf"`)
*   `sid` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** User-agent identifier (optional, default `"ezs-istex"`)
*   `token` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** authentication token (see [documentation](https://doc.istex.fr/api/access/fede.html#acc%C3%A8s-programmatique-via-un-token-didentification))

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)**&#x20;

### ISTEXScroll

Take an object containing a query string field and output records from the
ISTEX API. Every output record is merged with the input object.

#### Parameters

*   `query` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** ISTEX query (optional, default `input`)
*   `sid` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** User-agent identifier (optional, default `"ezs-istex"`)
*   `maxPage` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Maximum number of pages to get
*   `size` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** size of each page of results (optional, default `2000`)
*   `duration` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** maximum duration between two requests (optional, default `"5m"`)
*   `field` **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)>** fields to get (optional, default `["doi"]`)

#### Examples

```javascript
from([{ query: 'this is a test' }])
  .pipe(ezs('ISTEXScroll', {
      maxPage: 2,
      size: 1,
      sid: 'test',
  }))
```

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)<[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)>**&#x20;

### ISTEXTriplify

Take `Object` containing flatten hits from ISTEXResult.

If the environment variable DEBUG is set, some errors could appear on stderr.

See

*   [ISTEXResult](#istexresult)
*   [OBJFlatten](https://inist-cnrs.github.io/ezs/#/plugin-basics?id=objflatten) (from `@ezs/basics`)

<caption>data:</caption>

```js
{
  'author/0/name': 'Geoffrey Strickland',
  'author/0/affiliations/0': 'University of Reading',
  'host/issn/0': '0047-2441',
  'host/eissn/0': '1740-2379',
  'title': 'Maupassant, Zola, Jules Vallès and the Paris Commune of 1871',
  'publicationDate': '1983',
  'doi/0': '10.1177/004724418301305203',
  'id': 'F6CB7249E90BD96D5F7E3C4E80CC1C3FEE4FF483',
  'score': 1
}
```

<caption>javascript:</caption>

```js
.pipe(ezs('ISTEXTriplify', {
   property: [
     'doi/0 -> http://purl.org/ontology/bibo/doi',
     'language -> http://purl.org/dc/terms/language',
     'author/\\d+/name -> http://purl.org/dc/terms/creator',
     'author/\\d+/affiliations -> https://data.istex.fr/ontology/istex#affiliation',
   ],
 ));
```

 <caption>output:</caption>

```turtle
 <https://data.istex.fr/document/F6CB7249E90BD96D5F7E3C4E80CC1C3FEE4FF483>
    a <http://purl.org/ontology/bibo/Document> ;
      "10.1002/zaac.19936190205" ;
    <https://data.istex.fr/ontology/istex#idIstex> "F6CB7249E90BD96D5F7E3C4E80CC1C3FEE4FF483" ;
    <http://purl.org/dc/terms/creator> "Geoffrey Strickland" ;
    <https://data.istex.fr/ontology/istex#affiliation> "University of Reading" ;
```

#### Parameters

*   `property` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** path to uri for the properties to output (property and uri separated by `->`) (optional, default `[]`)
*   `source` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** the root of the keys (ex: `istex/`) (optional, default `""`)

Returns **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### ISTEXUniq

Remove duplicates triples within a single document's set of triples (same
subject).

Assume that every triple of a document (except the first one) follows another
triple of the same document.

<caption>Input:</caption>

```turtle
<https://api.istex.fr/ark:/67375/NVC-JMPZTKTT-R> <http://purl.org/dc/terms/creator> "S Corbett" .
<https://api.istex.fr/ark:/67375/NVC-JMPZTKTT-R> <https://data.istex.fr/ontology/istex#affiliation> "Department of Public Health, University of Sydney, Australia." .
<https://api.istex.fr/ark:/67375/NVC-JMPZTKTT-R> <https://data.istex.fr/ontology/istex#affiliation> "Department of Public Health, University of Sydney, Australia." .
<https://api.istex.fr/ark:/67375/NVC-JMPZTKTT-R> <https://data.istex.fr/ontology/istex#affiliation> "Department of Public Health, University of Sydney, Australia." .
```

<caption>Action in a `.ezs` script</caption>

```ini
[ISTEXUniq]
```

<caption>Output</caption>

```n3
<https://api.istex.fr/ark:/67375/NVC-JMPZTKTT-R> <http://purl.org/dc/terms/creator> "S Corbett" .
<https://api.istex.fr/ark:/67375/NVC-JMPZTKTT-R> <https://data.istex.fr/ontology/istex#affiliation> "Department of Public Health, University of Sydney, Australia." .
```

### ISTEXUnzip

Take the content of a zip file, extract JSON files, and yield JSON objects.

The zip file comes from dl.istex.fr, and the `manifest.json` is not
extracted.

Returns **any** Array<Object>
