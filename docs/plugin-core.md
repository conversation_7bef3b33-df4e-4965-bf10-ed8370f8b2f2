# core

Ce plugin propose une série d'instructions natives. Elles sont constamment
disponibles car chargées automatiquement.

## installation

```bash
npm install @ezs/core
```

## détails

Plusieurs instructions permettent de créer des sous-flux (*sub pipeline*), à
partir d'un fichier d’instructions ou d'instructions imbriquées. Si elles
s'utilisent toutes de la même manière (avec les mêmes paramètres) certaines
peuvent apparaître comme similaires mais leur fonctionnement est différent :

*   `[delegate]` : 1 sous-flux pour tous les éléments
*   `[swing]` : 1 sous-flux pour tous les éléments filtrés selon une condition
*   `[spawn]` : 1 sous-flux par élément
*   `[loop]` : 1 sous-flux par élément
*   `[expand]` : 1 sous-flux pour N éléments (N = size), seul le champ sélectionné est envoyé dans le pipeline
*   `[combine]` : 1 sous-flux pour tous les éléments, seul le champ sélectionné est comparé avec le résultat du sous-flux
*   `[singleton]` : 1 sous-flux pour le premier élément

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [assign](#assign)
*   [breaker](#breaker)
*   [combine](#combine)
*   [concat](#concat)
*   [debug](#debug)
*   [dedupe](#dedupe)
*   [delegate](#delegate)
*   [detach](#detach)
*   [dump](#dump)
*   [env](#env)
*   [exchange](#exchange)
*   [expand](#expand)
*   [extract](#extract)
*   [fork](#fork)
*   [group](#group)
*   [identify](#identify)
*   [ignore](#ignore)
*   [keep](#keep)
*   [loop](#loop)
*   [map](#map)
*   [metrics](#metrics)
*   [overturn](#overturn)
*   [pack](#pack)
*   [parallel](#parallel)
*   [pop](#pop)
*   [remove](#remove)
*   [replace](#replace)
*   [shift](#shift)
*   [shuffle](#shuffle)
*   [singleton](#singleton)
*   [spawn](#spawn)
*   [swing](#swing)
*   [throttle](#throttle)
*   [time](#time)
*   [tracer](#tracer)
*   [transit](#transit)
*   [truncate](#truncate)
*   [ungroup](#ungroup)
*   [unpack](#unpack)
*   [use](#use)
*   [validate](#validate)

### assign

*   **See**: [exchange](#exchange)

Affecte une valeur à un champ de l'objet courant.
Si le champ existe déjà, sa valeur est écrasée, sinon il est créé

Entrée:

```json
[{
    "nom": "un",
    "valeur": 1
},
{
    "nom": "deux",
    "valeur": 2
},
{
    "nom": "trois",
    "valeur": 3
},
{
    "nom": "quatre",
    "valeur": 4
}]
```

Script:

```ini
[assign]
path = valeur
value = get("valeur").multiply(2)
```

Output:

```json
[{
    "nom": "un",
    "valeur": 2
},
{
    "nom": "deux",
    "valeur": 4
},
{
    "nom": "trois",
    "valeur": 6
},
{
    "nom": "quatre",
    "valeur": 8
}]
```

Le `path` peut être le nom simple d'un champ présent à la racine de l'élément
traité, ou un chemin en [notation
pointée](https://goessner.net/articles/JsonPath/index.html#e2), en utilisant
une syntaxe proche de celle de la fonction
[`get`](https://lodash.com/docs/4.17.15#get) de Lodash.

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** chemin du champ à affecter
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** valeur à affecter

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### breaker

Break the stream  if the control file cannot be checked

#### Parameters

*   `fusible` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** file to check

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### combine

Takes an `Object` and substitute a field with the corresponding value found in a external pipeline
the internal pipeline must produce a stream of special object (id, value)

```json
[
          { year: 2000, dept: 54 },
          { year: 2001, dept: 55 },
          { year: 2003, dept: 54 },
]
```

Script:

```ini
[use]
plugin = analytics

[combine]
path = dept
file = ./departement.ini

```

Output:

```json
 [
          { year: 2000, dept: { id: 54, value: 'Meurthe et moselle' } },
          { year: 2001, dept: { id: 55, value: 'Meuse' } },
          { year: 2003, dept: { id: 54, value: 'Meurthe et moselle' } },
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the path to substitute
*   `default` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** value if no substitution (otherwise value stay unchanged)
*   `primer` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** Data to send to the external pipeline (optional, default `n/a`)
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
*   `cacheName` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Enable cache, with dedicated name

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### concat

Take all `String`, concat them and throw just one.

```json
[
     "a",
     "b",
     "c"
]
```

Script:

```ini
[concat]
beginWith = <
joinWith = |
endWith = >
```

Output:

```json
[
     "<a|b|c>"
]
```

#### Parameters

*   `beginWith` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Add value at the begin
*   `joinWith` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** use value to join 2 chunk
*   `endWith` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Add value at the end

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### debug

Take `Object`, print it (with its index number), and throw the same object.

#### Parameters

*   `level` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** DEBUG ezs level (depends of DEBUG env variable, see cli parameters) (optional, default `info`)
*   `text` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** text before the dump (optional, default `valueOf`)
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path of field to print

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### dedupe

Take `Object`, and check that the object identifier has not already been used previously

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path containing the object Identifier (optional, default `uri`)
*   `ignore` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** Just ignore duplicate object (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### delegate

Delegate processing to an external pipeline.

> **Note**: works like [spawn](#spawn), but each chunk share the same external pipeline.

#### Parameters

*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### detach

Delegate processing to an external pipeline.

> **Note**: works like [spawn](#spawn), but each chunk share the same external pipeline.

#### Parameters

*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
*   `encoder` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** The statement to encode each chunk to a string (optional, default `pack`)
*   `decoder` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** The statement to decode each chunk as a string (optional, default `unpack`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### dump

Take all `Object`s and generate a JSON array

```json
[
    { "a": 1 },
    { "a": 2 },
    { "a": 3 },
    { "a": 4 },
    { "a": 5 }
]
```

Script:

```ini
[dump]
indent = true
```

Output:

```json
 [{
   "a": 1
  },
  {
   "a": 2
  },
  {
   "a": 3
  },
  {
   "a": 4
  },
  {
   "a": 5
  }
]
```

#### Parameters

*   `indent` **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** indent JSON (optional, default `false`)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### env

Crée une variable d'environnement globale à tout le script.

On l'utilise en général au début du script (après `[use]`).

Pour utiliser la variable, il faut employer la fonction `env()`.

Entrée:

```json
[{
    "nom": "un",
    "valeur": 1
},
{
    "nom": "deux",
    "valeur": 2
}]
```

Script:

```ini
[use]
plugin = basics

[env]
path = nom
value = NOM GÉNÉRIQUE

[JSONParse]

[assign]
path = nom
value = env("nom")

[dump]
indent = true
```

Sortie:

```json
[{
    "nom": "NOM GÉNÉRIQUE",
    "valeur": 1
},
{
    "nom": "NOM GÉNÉRIQUE",
    "valeur": 2
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** nom de la variable à créer
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** valeur de la variable

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### exchange

*   **See**: [assign](#assign)
*   **See**: [extract](#extract)

Remplace tout un objet par un autre (au sens JSON).

Entrée:

````json
[{
   "nom": "un",
   "valeur": 1
},
{
   "nom": "deux",
   "valeur": 2
}]

Script:

```ini
[use] plugin = basics

[JSONParse]

[exchange] value = get("nom")

[dump]
````

Sortie:

```json
["un","deux"]
```

Ici, 'objet `{"nom":"un","valeur":1}` a été remplacé par l'« objet » (au sens
JSON, une chaîne de caractères, tout autant qu'un nombre, constitue un objet)
`"un"`.

Note: `assign` ne permet pas de remplacer tout l'objet, mais seulement une de
ses propriétés.

Entrée:

```json
[{
   "a": "abcdefg", "b": "1234567", "c": "XXXXXXX"
},
{
   "a": "abcdefg", "b": "1234567", "c": "XXXXXXX"
}]
```

Script:

```ini
[exchange]
value = omit('c')
```

Output:

```json
[{
   "a": "abcdefg",
   "b": "1234567"
},
{
   "a": "abcdefg",
   "b": "1234567"
}]
```

Ici, on a remplacé un objet avec trois propriétés par le même objet sans la
propriété `c` (voir la function
[`omit`](https://lodash.com/docs/4.17.15#omit) de Lodash).

#### Parameters

*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** la valeur de remplacement de l'objet courant

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### expand

Takes an `Object` and substitute a field with the corresponding value found in a external pipeline
the internal pipeline receive a special object { id, value } id is the item identifier & value is the item path value
The internal pipeline can expand value with another

```json
[
          { year: 2000, dept: 54 },
          { year: 2001, dept: 55 },
          { year: 2003, dept: 54 },
]
```

Script:

```ini
[use]
plugin = analytics

[expand]
path = dept
file = ./departement.ini

```

Output:

```json
 [
          { year: 2000, dept: { id: 54, value: 'Meurthe et moselle' } },
          { year: 2001, dept: { id: 55, value: 'Meuse' } },
          { year: 2003, dept: { id: 54, value: 'Meurthe et moselle' } },
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the path to substitute
*   `size` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** How many chunk for sending to the external pipeline (optional, default `1`)
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
*   `cacheName` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Enable cache, with dedicated name
*   `token` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** add token values in the subpipeline (optional)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### extract

*   **See**: [assign](#assign)
*   **See**: [exchange](#exchange)

Extrait de l'objet courant les valeurs de certains champs, et renvoie
directement les valeurs dans le flux de sortie.

> **Note**: `extract` ne peut pas fournir des valeurs `undefined` ou `null`.

Entrée:

```json
[{
   "nom": "un",
   "valeur": 1,
   "important": false
},
{
   "nom": "deux",
   "valeur": 2,
   "important": true
}]
```

Script:

```ini
[use]
plugin = basics

[JSONParse]

[extract]
path = valeur
path = nom

[dump]
```

Sortie:

```json
[[1,"un"],[2,"deux"]]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** chemin d'un champ à extraire

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### fork

fork the current pipeline

> **Note**: but each chunk is sent to the same external pipeline.

#### Parameters

*   `standalone` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** The current pipeline will be able to end without waiting for the end of the external pipeline (optional, default `false`)
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
*   `target` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** choose the key to set with the forked request identifier (optional, default `x-request-id`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### group

Take all `chunk`s, and throw them grouped by `length`.

See also [ungroup](#ungroup).

```json
[
     "a",
     "b",
     "c",
     "d",
     "e",
     "f",
     "g",
     "h"
]
```

Script:

```ini
[group]
length = 3
```

Output:

```json
[
     [ "a", "b", "c" ],
     [ "d", "e", "f" ],
     [ "g", "h" ]
]
```

#### Parameters

*   `size` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)?** Size of each partition (alias: length)
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path to set with the array

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### identify

Take `Object`, and compute & add an identifier

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `scheme` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** scheme to use (uid or sha) (optional, default `uid`)
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** path containing the object Identifier (optional, default `uri`)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### ignore

Takes all the chunks, and ignore the firtst N chunk

Input file:

```json
[{
   "a": 1
},
{
   "a": 2
},
{
   "a": 3
},
{
   "a": 4
},
{
   "a": 5
}]
```

Script:

```ini
[ignore]
length = 3
```

Output:

```json
[{
   "a": 4
},
{
   "a": 5
}]
```

#### Parameters

*   `length` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)?** Length of the feed to ignore

Returns **any**&#x20;

### keep

Throw input `Object` but keep only specific fields.

Input file:

```json
[{
   "a": "abcdefg",
   "b": "1234567",
   "c": "XXXXXXX"
},
{
   "a": "abcdefg",
   "b": "1234567",
   "c": "XXXXXXX"
}]
```

Script:

```ini
[keep]
path = a
path = b
```

Output:

```json
[{
   "a": "abcdefg",
   "b": "1234567"
},
{
   "a": "abcdefg",
   "b": "1234567"
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path of field to keep

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### loop

Loop on external pipeline, until test will be true

> **Note**: works like [delegate](#delegate), but each chunk use its own external pipeline

#### Parameters

*   `test` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** if test is true
*   `reverse` **[Boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** to reverse the test (optional, default `false`)
*   `maxDepth` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** to limit the number of loops (optional, default `100000`)
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
*   `fusible` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Can be set with the ezs server fusible see env('request.fusible')

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### map

From an array field delegate processing of each items to an external pipeline

> **Note**: works like [delegate](#delegate), but each chunk use its own external pipeline

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the path to substitute
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### metrics

*   **See**: ../server/knownPipeline.js

Take `Object`, and throw the same object.

This statement will only be used if :

*   EZS\_METRICS is enabled
*   ezs is running in server mode

WARNING: avoid setting bucket to "input" or "output", as these labels are used by ezs.
If you do, you risk distorting the associated metrics.

#### Parameters

*   `pathName` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** to identify the script (optional, default `auto`)
*   `bucket` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** to identify the moment of measurement (optional, default `unknow`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### overturn

Takes an `Object` and substitute twice a field with the corresponding value found in a external pipeline
the internal pipeline receive a special object { id, value, token } :

*   id is the item identifier
*   value is the item path value,
*   token is an array containing stream id and an number (0 for first time, 1 for the second tme
    The internal pipeline can overturn value with another.

It's work like \[expand] but
the second call starts only when all the values of the stream have been sent once

```json
[
          { year: 2000, dept: 'Meuse' },
          { year: 2001, dept: 'Moselle' },
          { year: 2003, dept: 'Vosges'},
]
```

Script #1:

```ini
[overturn]
path = dept

[overturn/assign]
path = value
value = get('value').split('').reverse().join('')

```

Output:

```json
 [
          { year: 2000, dept: 'Meuse' },
          { year: 2001, dept: 'Moselle' },
          { year: 2003, dept: 'Vosges' },
 ]
```

Script #2:

```ini
[overturn]
path = dept

[overturn/drop]
  path = token.1
  if = 0

[overturn/assign]
path = value
value = get('value').split('').reverse().join('')

```

Output:

```json
 [
          { year: 2000, dept: 'esueM' },
          { year: 2001, dept: 'ellesoM' },
          { year: 2003, dept: 'segsoV' },
 ]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the path to overturn
*   `size` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** How many chunk for sending to the external pipeline (optional, default `1`)
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### pack

Take all `Object`, throw encoded `String`

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path to set in the serialize object (default: none)

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### parallel

Takes an `Object` delegate processing to X internal pipelines

#### Parameters

*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### pop

*   **See**: shift

Return the last `Object` and close the feed

Input file:

```json
[{
   "a": 1
},
{
   "a": 2
},
{
   "a": 3
},
{
   "a": 4
},
{
   "a": 5
}]
```

Script:

```ini
[shift]
```

Output:

```json
[{
   "a": 5
}]
```

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### remove

Take `Object` and remove it from the feed if test is true
Input file:

```json
[{
   a: "a"
},
{
   a: 2
},
{
   a: "b"
},
{
   a: 4
},
{
   a: "c"
}]
```

Script:

```ini
[remove]
test = get('a).isInteger()
reverse = true
```

Output:

```json
[
    {
       a: 2
    },
    {
       a: 4
    }
]
```

#### Parameters

*   `test` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** if test is true
*   `reverse` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** reverse the test (optional, default `false`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### replace

Take `Object` and replace it with a new object with some fields.

See also [exchange](#exchange) and [assign](#assign).

Input file:

```json
[{
   "a": 1
},
{
   "a": 2
},
{
   "a": 3
},
{
   "a": 4
},
{
   "a": 5
}]
```

Script:

```ini
[replace]
path = b.c
value = 'X'
```

Output:

```json
[{
   "b": { "c": "X" }
},
{
   "b": { "c": "X" }
},
{
   "b": { "c": "X" }
},
{
   "b": { "c": "X" }
},
{
   "b": { "c": "X" }
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path of the new field
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** value of the new field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### shift

Return the first `Object` and close the feed

Input file:

```json
[{
   "a": 1
},
{
   "a": 2
},
{
   "a": 3
},
{
   "a": 4
},
{
   "a": 5
}]
```

Script:

```ini
[shift]
```

Output:

```json
[{
   "a": 1
}]
```

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### shuffle

Take `Object`, shuffle data of the whole object or only some fields specified by path

Input file:

```json
[{
   "a": "abcdefg",
   "b": "1234567"
},
{
   "a": "abcdefg",
   "b": "1234567"
}]
```

Script:

```ini
[shuffle]
path = a
```

Output:

```json
[{
   "a": "cadbefg",
   "b": "1234567"
},
{
   "a": "dcaegbf",
   "b": "1234567"
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path of field to shuffle

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### singleton

Takes only the first  `Object` delegate processing to a external pipeline

#### Parameters

*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### spawn

Delegate processing to an external pipeline, throw each chunk from the result.

> **Note**: works like [delegate](#delegate), but each chunk use its own external pipeline

#### Parameters

*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an URL-like command
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
*   `cache` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Use a specific ezs statement to run commands (advanced)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### swing

Delegate processing to an external pipeline under specifics conditions

> **Note**: works like [spawn](#spawn), but each chunk shares the same external
> pipeline.

#### Parameters

*   `test` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** if test is true
*   `reverse` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** reverse the test (optional, default `false`)
*   `file` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a file
*   `script` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in a string of characters
*   `commands` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an object
*   `command` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** the external pipeline is described in an URL-like
*   `logger` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** A dedicaded pipeline described in a file to trap or log errors
    command

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### throttle

Take `Object` and return the same object

```json
[{
         { id: 'x', value: 2 },
         { id: 't', value: 2 },
}]
```

Script:

```ini
[use]
plugin = analytics

[throttle]
bySecond = 2

```

Output:

```json
[
         { id: 'x', value: 2 },
         { id: 't', value: 2 },
]
```

#### Parameters

*   `bySecond` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Number of object by second (optional, default `1`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### time

Measure the execution time of a script, on each chunk of input.

#### Parameters

*   `script` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?**&#x20;

#### Examples

Input

```javascript
[1]
```

Program

```javascript
const script = `
[transit]
`;
from([1])
    .pipe(ezs('time', { script }))
```

Output

```javascript
[{
  data: 1,
  time: 15 // milliseconds
}]
```

Returns **[object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### tracer

Take `Object`, print a character and throw the same object.
Useful to see the progress in the stream.

#### Parameters

*   `print` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** character to print at each object (optional, default `.`)
*   `last` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** character to print at last call (optional, default `.`)
*   `first` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** character to print at first call (optional, default `.`)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### transit

Take `Object` and throw the same object again.

Input file:

```json
[{
   "a": 1
},
{
   "a": 2
}]
```

Script:

```ini
[transit]
```

Output:

```json
[{
   "a": 1
},
{
   "a": 2
}]
```

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### truncate

Takes all the chunks, and closes the feed when the total length is equal to the parameter.

Input file:

```json
[{
   "a": 1
},
{
   "a": 2
},
{
   "a": 3
},
{
   "a": 4
},
{
   "a": 5
}]
```

Script:

```ini
[truncate]
length = 3
```

Output:

```json
[{
   "a": 1
},
{
   "a": 2
},
{
   "a": 3
}]
```

#### Parameters

*   `length` **[Number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)?** Length of the feed

Returns **any**&#x20;

### ungroup

Take all `chunk`s, and throw one item for every chunk.

See also [group](#group).

```json
[
     [ "a", "b", "c" ],
     [ "d", "e", "f" ],
     [ "g", "h" ]
]
```

Script:

```ini
[ungroup]
```

Output:

```json
[
     "a",
     "b",
     "c",
     "d",
     "e",
     "f",
     "g",
     "h"
]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path to get the array

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)\<any>**&#x20;

### unpack

Take `String`s or `Buffer`s and throw `Object` builded by JSON.parse on each line.

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path to get in the serialize object (default: none)

Returns **[object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### use

Take all `String`, concat them and throw just one.

Script:

```ini
[use]
plugin = basics
plugin = analytics
```

#### Parameters

*   `beginWith` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Add value at the begin
*   `joinWith` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** use value to join 2 chunk
*   `endWith` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Add value at the end

Returns **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**&#x20;

### validate

From an `Object`, throw the same object if all rules pass

See

*   [Laravel validator rules](https://laravel.com/docs/8.x/validation)
*   <https://github.com/skaterdav85/validatorjs#readme>

Input file:

```json
[{
   "a": 1,
   "b": "titi"
},
{
   "a": 2,
   "b": "toto"
},
{
   "a": false
},
]
```

Script:

```ini
[validate]
path = a
rule = required|number

path = a
rule = required|string
```

Output:

```json
[{
   "a": 1,
   "b": "titi"
},
{
   "a": 2,
   "b": "toto"
}]
```

#### Parameters

*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path of the field
*   `rule` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** rule to validate the field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;
