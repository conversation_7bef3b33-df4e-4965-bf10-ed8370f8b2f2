# ark

Ce plugin propose une série d'instructions pour utiliser, générer des identifiants ARK

## installation

```bash
npm install @ezs/ark
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [generateARK](#generateark)

### generateARK

Take `Object` object, and throw the same object but with an ARK

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `target` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field name to insert ARK (optional, default `ark`)
*   `subpublisher` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Subpublisher
*   `naan` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** NAAN
*   `databasePath` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Path for database to store generated ARKs

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;
