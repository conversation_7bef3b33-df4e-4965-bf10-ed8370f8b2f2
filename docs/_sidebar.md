- Présentation

  - [<PERSON><PERSON><PERSON><PERSON>](quickstart.md)
  - [Plugin](plugin.md)
  - [Instruction](statement.md)
  - [Fichier .ini](ini.md)

- Fonctionnement

  - [Instruction](coding-statement.md)
  - [Fichier .ini](coding-ini.md)
  - [Sous flux](coding-sub-pipeline.md)
  - [Mode server](coding-server.md)

- Usage

  - [Ligne de commandes](cli.md)
  - [Serveur de traitements](server.md)
  - [API](api.md)

- Exemples

  - [Conversion de flux](example-conversions.md)
  - [Parallèlisation de traitements](example-parallel.md)
  - [Enrichissement par web API](example-enrichments.md)

- Plugins

  - [analytics](plugin-analytics.md)
  - [ark](plugin-ark.md)
  - [basics](plugin-basics.md)
  - [conditor](plugin-conditor.md)
  - [core](plugin-core.md)
  - [istex](plugin-istex.md)
  - [libpostal](plugin-libpostal.md)
  - [loterre](plugin-loterre.md)
  - [sparql](plugin-sparql.md)
  - [spawn](plugin-spawn.md)
  - [storage](plugin-storage.md)
  - [strings](plugin-strings.md)
  - [teeft](plugin-teeft.md)
  - [transformers](plugin-transformers.md)
  - [xslt](plugin-xslt.md)

- **Links**
- [![Github](https://icongr.am/devicon/github-original.svg)Github](https://github.com/Inist-CNRS/ezs)
- [![NPM](https://icongr.am/devicon/npm-original-wordmark.svg)NPM](https://www.npmjs.com/search?q=keywords:ezs)
