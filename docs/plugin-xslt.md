# xslt

## Présentation

Ce plugin propose une série d'instructions pour utiliser des transformations XSLT

## installation

```bash
npm install @ezs/xslt
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [xslt](#xslt)
*   [xslt](#xslt-1)

### xslt

Send all received {String} to a host FOP Processor

Script:

```ini
[use]
plugin = xslt

[fop]

```

#### Parameters

*   `config` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** argument for FOP Processor

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### xslt

Send all received {String} to a host XSL Processor

Script:

```ini
[use]
plugin = xslt

[xslt]
stylesheet = ./style.xsl
param = prefix=X

```

#### Parameters

*   `stylesheet` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** path to stylesheet file
*   `param` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** argument for XSL Processor

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;
