# transformers

Ce plugin propose une série d'instructions spécifiques de transformation sur un seul champ d'un objet.
Cette notion est utilisée dans [lodex](https://lodex.inist.fr) pour transformer les valeurs des colonnes à charger.

Chaque instruction possède le paramètre `field`.
Il s'agit du chemin vers le champ à transformer ou le chemin vers un nouveau champ.

Il est possible d'enchaîner les transformations sur un même champ :

```ini
[$PREFIX]
field = tag
with = <

[$SUFFIX]
field = tag
with = >
```

Comme cette série d'instructions fonctionne uniqument sur un seul champ (et non sur la totalité comme les autres plugins),
elle génère en sortie un champ "technique" nommé `$origin` qui contient l'objet tel qu'il était avant d'appliquer un traitement.

Ce champ peut être facilement supprimé :

```ini
[$UPPERCASE]
field = title

[exchange]
value = omit('$origin')
```

## exemple

```json
  [
            { "id": 1, "title": "un" },
            { "id": 2, "title": "deux" },
            { "id": 3, "title": "trois" },
  ]
```

Script:

```ini
[use]
plugin = transformers

[$UPPERCASE]
field = title

[$SUFFIX]
field = title
with = §

[$STRING]
field = id

[$PREFIX]
field = id
with = uri:/
```

Output:

```json
   [
            { "id": "uri:/1", "title": "UN§" },
            { "id": "uri:/2", "title": "DEUX§" },
            { "id": "uri:/3", "title": "TROIS§" },
   ]
```

## installation

```bash
npm install @ezs/transformers
```

## usage

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

*   [$ARRAY](#array)
*   [$BOOLEAN](#boolean)
*   [$CAPITALIZE](#capitalize)
*   [$COLUMN](#column)
*   [$CONCAT](#concat)
*   [$CONCAT\_URI](#concat_uri)
*   [$DEFAULT](#default)
*   [$FORMAT](#format)
*   [$GET](#get)
*   [$JOIN](#join)
*   [$LOWERCASE](#lowercase)
*   [$MAPPING](#mapping)
*   [$MASK](#mask)
*   [$NUMBER](#number)
*   [$PARSE](#parse)
*   [$PREFIX](#prefix)
*   [$REMOVE](#remove)
*   [$REPLACE](#replace)
*   [$REPLACE\_REGEX](#replace_regex)
*   [$SELECT](#select)
*   [$SHIFT](#shift)
*   [$SPLIT](#split)
*   [$STRING](#string)
*   [$SUFFIX](#suffix)
*   [$TRIM](#trim)
*   [$TRUNCATE](#truncate)
*   [$TRUNCATE\_WORDS](#truncate_words)
*   [$UNIQ](#uniq)
*   [$UPPERCASE](#uppercase)
*   [$URLENCODE](#urlencode)
*   [$VALUE](#value)

### $ARRAY

transformer une chaîne de caractères en tableau avec un seul element

Exemple :

```ini
[$ARRAY]
field = keywords

[exchange]
value = omit('$origin')
```

Entrée:

```json
[{
  "keywords": "un"
}, {
  "keywords": "deux"
}]
```

Sortie:

```json
[{
  "keywords": ["un"]
}, {
  "keywords": ["deux"]
}]
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $BOOLEAN

transformer une chaîne de caractères en booléen

Exemple :

```ini
[$BOOLEAN]
field = haveMoney

[exchange]
value = omit('$origin')
```

Entrée:

```json
[{
  "name": "Chuck",
  "haveMoney": 10000
}, {
  "name": "Charlot",
  "haveMoney": "yes"
}, {
  "name": "Alan",
  "haveMoney": 1
}]
```

Sortie:

```json
[{
  "name": "Chuck",
  "haveMoney": false
}, {
  "name": "Charlot",
  "haveMoney": true
}, {
  "name": "Alan",
  "haveMoney": true
}]
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $CAPITALIZE

mettre le premier caractère en majuscule

Exemple :

```ini
[$CAPITALIZE]
field = title
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $COLUMN

prendre une donnée dans un champ (colonne d'un fichier tabulé)

Exemple :

```ini
[$COLUMN]
field = newTitle
column = oldTitle
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `column` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to use during the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $CONCAT

concaténer deux valeurs

Exemple :

```ini
[$CONCAT]
field = result
columns = part1
columns = part2
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get the result of the transformation
*   `columns` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get value

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $CONCAT\_URI

composer un identifiant avec plusieurs champs

Exemple :

```ini
[$CONCAT_URI]
field = identifiant
column = nom
column = prenom
separator = -
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get the result of the transformation
*   `column` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get data
*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** glue between each column

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $DEFAULT

donner une valeur par défaut

Exemple :

```ini
[$DEFAULT]
field = title
alternative = not available
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `alternative` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to use if field does not exist

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $FORMAT

appliquer un patron (template)

```ini
[$DEFAULT]
field = source
with = (%s:%s)
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get data source (must be an array)
*   `with` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** template string like sprintf

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $GET

Récupère toutes les valeurs correspondant à un chemin (dot path)

Exemple :

```ini
[$GET]
field = output
path = input
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get the result of the transformation
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get value

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $JOIN

Rassemble les valeurs d'un tableau en une chaîne de caractères

Exemple :

```ini
[$JOIN]
field = output
path = input
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation (must be an array)
*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** glue between each field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $LOWERCASE

mettre en bas de casse (minuscules)

Exemple :

```ini
[$LOWERCASE]
field = title
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $MAPPING

Opération permettant le remplacement à partir d'une table
(équivalent à l'enchaînement de plusieurs opérations [$REPLACE](#replace))

Exemple :

```ini
[$MAPPING]
field = keywords
list = "hello":"bonjour", "hi":"salut"
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `list` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** the mapping list

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $MASK

S'assure que la valeur respecte une expression régulière

Exemple :

```ini
[$MASK]
field = title
with = ^[a-z]+$
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the control
*   `with` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** regular expression to check

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $NUMBER

transformer une chaîne de caractères en nombre

Exemple :

```ini
[$NUMBER]
field = counter
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $PARSE

Analyser un chaine de caractère comme étant du JSON

Exemple :

```ini
[$PARSE]
field = json
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $PREFIX

préfixer la valeur avec une chaîne de caractères

Exemple :

```ini
[$PREFIX]
field = title
with = #
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `with` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to add at the begining of the field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $REMOVE

supprimer un élément ou une sous-chaîne

Exemple :

```ini
[$REMOVE]
field = title
the = .
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `the` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to drop in the field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $REPLACE

remplacer une chaîne par une autre

Exemple :

```ini
[$REPLACE]
field = title
searchValue = 1
replaceValue = un
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `searchValue` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to search
*   `replaceValue` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to replace with

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $REPLACE\_REGEX

remplacer une chaîne par une autre via une exrpression régulière

Exemple :

```ini
[$REPLACE_REGEX]
field = title
searchValue = $hel\w+
replaceValue = bonjour
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `searchValue` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** regex to search
*   `replaceValue` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to replace with

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $SELECT

Prendre une valeur dans un objet à partir de son chemin (dot path)

Exemple :

```ini
[$SELECT]
field = title
path = en
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get the result of the selection
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to get value

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $SHIFT

décaler une valeur multiple (tableau ou chaîne de caractères)

Exemple :

```ini
[$SHIFT]
field = title
gap = 2
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `path` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `gap` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** how many items or characters to drop

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $SPLIT

segmente une chaîne de caractères en tableau

Exemple :

```ini
[$SPLIT]
field = title
separator = |
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `separator` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to use to split the field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $STRING

transforme la valeur en chaîne de caractères

Exemple :

```ini
[$STRING]
field = title
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $SUFFIX

ajoute une chaîne de caractères à la fin d'une chaîne ou d'un tableau

Exemple :

```ini
[$SUFFIX]
field = title
with = !
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `with` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to add at the end of the field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $TRIM

enlève les espaces au début et à la fin d'une chaîne de caractères

Exemple :

```ini
[$TRIM]
field = title
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $TRUNCATE

tronque, prend les premières valeurs d'un tableau, d'une chaîne

Exemple :

```ini
[$TRUNCATE]
field = title
gap = 25
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation
*   `gap` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** how many items or characters to keep

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $TRUNCATE\_WORDS

Opération permettant la troncature par nombre de mots
et non pas par nombre de caractères comme pour l'opération [$TRUNCATE](#truncate)

Exemple :

```ini
[$TRUNCATE_WORDS]
field = title
gap = 10
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** field path to apply the transformation
*   `gap` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** how many words to keep

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $UNIQ

dédoublonne les valeurs (dans un tableau)

Exemple :

```ini
[$UNIQ]
field = title
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation (must be an aarray)

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $UPPERCASE

mettre la chaîne en majuscules

Exemple :

```ini
[$UPPERCASE]
field = title
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $URLENCODE

encode une chaine comme dans une URL

Exemple :

```ini
[$URLENCODE]
field = url
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** field path to apply the transformation

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;

### $VALUE

Fixer une valeur

Exemple :

```ini
[$VALUE]
field = title
value = Hello world
```

#### Parameters

*   `data` &#x20;
*   `feed` &#x20;
*   `field` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** new field path
*   `value` **[String](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** value to use to set the field

Returns **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**&#x20;
