<!DOCTYPE html>
<html lang="fr">
    <head>
        <meta charset="UTF-8">
        <title>EZS / Documentation</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="description" content="Description">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsify-themeable@0/dist/css/theme-simple.css">
    </head>
    <body>
        <div id="app"></div>
        <script src="//cdn.jsdelivr.net/npm/docsify-themeable@0"></script>
        <script src="//unpkg.com/docsify-edit-on-github/index.js"></script>
        <script>
            window.$docsify = {
                logo: '/_media/icon.svg',
                name: '@ezs',
                repo: 'Inist-CNRS/ezs',
                loadNavbar: false,
                loadSidebar: true,
                coverpage: true,
                auto2top: true,
                maxLevel: 2,
                subMaxLevel: 2,
                plugins: [
                    EditOnGithubPlugin.create(
                        'https://github.com/Inist-CNRS/ezs/blob/master/docs/'
                    ),
                ],
                search: {
                    depth: 3
                },
                pagination: {
                    previousText: 'Précédent',
                    nextText: 'Suivant',
                    crossChapter: true,
                    crossChapterText: true,
                },
            }
        </script>
        <script src="//unpkg.com/docsify/lib/docsify.min.js"></script>
        <script src="//unpkg.com/docsify/lib/plugins/search.min.js"></script>
        <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/zoom-image.min.js"></script>
        <script src="//unpkg.com/docsify-copy-code@2"></script>
        <script src="//unpkg.com/docsify-pagination/dist/docsify-pagination.min.js"></script>
        <script src="//unpkg.com/prismjs/components/prism-ini.min.js"></script>
        <script src="//unpkg.com/prismjs/components/prism-bash.min.js"></script>
        <script src="//unpkg.com/prismjs/components/prism-json.min.js"></script>
        <script src="//unpkg.com/prismjs/components/prism-javascript.min.js"></script>
    </body>
</html>
