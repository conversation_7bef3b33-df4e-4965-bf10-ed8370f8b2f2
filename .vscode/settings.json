{
    "js/ts.implicitProjectConfig.checkJs": true,
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": false,
    },
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit"
    },
    "editor.tabSize": 4,
    "prettier.configPath": "./.prettierrc",
    "makefile.configureOnOpen": false,
    "cSpell.words": [
        "conditor",
        "Conditor",
        "corhal",
        "CORHAL",
        "preversion",
        "requêter",
        "rnsr",
        "Rnsr",
        "sparql",
        "Transpiling"
    ]
}
