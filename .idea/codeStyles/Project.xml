<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="AUTODETECT_INDENTS" value="false" />
    <CssCodeStyleSettings>
      <option name="HEX_COLOR_LOWER_CASE" value="true" />
      <option name="HEX_COLOR_SHORT_FORMAT" value="true" />
      <option name="VALUE_ALIGNMENT" value="2" />
      <option name="KEEP_SINGLE_LINE_BLOCKS" value="true" />
    </CssCodeStyleSettings>
    <JSCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="ALIGN_OBJECT_PROPERTIES" value="2" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="VAR_DECLARATION_WRAP" value="2" />
      <option name="OBJECT_LITERAL_WRAP" value="2" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="USE_CHAINED_CALLS_GROUP_INDENTS" value="true" />
    </JSCodeStyleSettings>
    <MarkdownNavigatorCodeStyleSettings>
      <option name="RIGHT_MARGIN" value="72" />
    </MarkdownNavigatorCodeStyleSettings>
    <codeStyleSettings language="CSS">
      <indentOptions>
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="HTML">
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="RIGHT_MARGIN" value="120" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="INDENT_CASE_FROM_SWITCH" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_FOR" value="false" />
      <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
      <option name="CALL_PARAMETERS_WRAP" value="5" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="2" />
      <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
      <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_WRAP" value="5" />
      <option name="IF_BRACE_FORCE" value="1" />
      <option name="DOWHILE_BRACE_FORCE" value="1" />
      <option name="WHILE_BRACE_FORCE" value="1" />
      <option name="FOR_BRACE_FORCE" value="1" />
    </codeStyleSettings>
  </code_scheme>
</component>