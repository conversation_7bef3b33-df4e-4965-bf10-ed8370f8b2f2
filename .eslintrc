{"plugins": ["prettier"], "parser": "babel-es<PERSON>", "env": {"es2020": true, "browser": true, "mocha": true, "node": true, "jest": true}, "extends": ["airbnb-base", "prettier"], "rules": {"indent": ["error", 4], "max-len": ["error", {"code": 120, "tabWidth": 4, "ignoreUrls": true}], "no-console": ["error", {"allow": ["warn"]}], "no-underscore-dangle": ["error", {"allow": ["_id", "_error"]}], "import/no-extraneous-dependencies": "off", "no-param-reassign": ["error", {"props": false}], "semi": ["error", "always"], "quotes": ["error", "single", {"avoidEscape": false}], "import/extensions": ["error", "always"]}}