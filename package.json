{"name": "ezs", "private": true, "workspaces": ["packages/*"], "engines": {"node": ">=14.0.0", "yarn": ">= 1.0.0", "npm": "Please use Yarn instead of NPM to install dependencies. See: https://yarnpkg.com/lang/en/docs/install/"}, "devDependencies": {"@babel/cli": "7.28.0", "@babel/core": "7.28.0", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-json-strings": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/plugin-transform-modules-commonjs": "7.27.1", "@babel/preset-env": "7.28.0", "@babel/register": "7.27.1", "@types/jest": "30.0.0", "@types/ramda": "0.31.0", "array-parallel": "0.1.3", "array-series": "0.1.5", "babel-eslint": "10.1.0", "babel-jest": "30.0.5", "coveralls": "3.1.1", "documentation": "14.0.3", "eslint": "9.32.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "2.32.0", "eslint-plugin-prettier": "5.5.4", "feed": "4.2.2", "from": "0.1.7", "get-stream": "6.0.1", "git-cz": "4.7.6", "jest": "26.6.3", "lerna": "4.0.0", "nock": "13.0.11", "node-fetch": "2.7.0", "prettier": "2.3.2", "semver": "7.5.2", "sleepjs": "3.0.1"}, "scripts": {"release:version": "lerna version --exact --conventional-commits", "release:publish": "lerna publish from-package", "commit": "git status && npx git-cz -a", "build": "yarn workspaces run build", "ci:install": "yarn install --ignore-engines --frozen-lockfile && yarn run build", "ci:test": "yarn run test:all", "doc": "yarn workspaces run doc", "lint": "yarn workspaces run --no-bail lint", "pretest": "yarn install && yarn run build", "test": "yarn run test:all", "test:all": "NODE_ENV=test jest --maxWorkers 4 --verbose", "test:debug": "NODE_ENV=test jest --runInBand --detectOpenHandles", "coverage": "NODE_ENV=test jest --ci --runInBand --reporters=default --collectCoverage --coverageReporters=text --coverage", "coverage:html": "NODE_ENV=test jest --ci --runInBand --reporters=default --collectCoverage --coverageReporters=html --coverage", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "config": {"commitizen": {"path": "git-cz"}}}